<script>
  import { onMount } from 'svelte';

  // 导入页面组件
  import Dashboard from './pages/Dashboard.svelte';

  // 应用状态
  let isLoading = true;
  let initError = null;

  onMount(async () => {
    try {
      console.log('应用启动');
      // 简化初始化
      setTimeout(() => {
        isLoading = false;
      }, 1000);
    } catch (error) {
      console.error('应用初始化失败:', error);
      initError = error;
      isLoading = false;
    }
  });
</script>

<main class="app">
  {#if isLoading}
    <div class="loading-screen">
      <div class="loading-spinner"></div>
      <p>正在初始化应用...</p>
    </div>
  {:else if initError}
    <div class="error-screen">
      <h2>应用初始化失败</h2>
      <p>{initError.message}</p>
      <button on:click={() => window.location.reload()}>重新加载</button>
    </div>
  {:else}
    <div class="app-layout">
      <header class="app-header">
        <h1>AI小说助手</h1>
      </header>

      <main class="main-content">
        <Dashboard />
      </main>
    </div>
  {/if}
</main>

<style>
  .app {
    height: 100vh;
    overflow: hidden;
  }

  .app-layout {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--md-sys-color-background);
  }

  .app-header {
    padding: 16px 24px;
    background: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
  }

  .app-header h1 {
    margin: 0;
    font-size: 24px;
  }

  .main-content {
    flex: 1;
    overflow-y: auto;
  }

  .loading-screen,
  .error-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: var(--md-sys-color-background);
    color: var(--md-sys-color-on-background);
  }

  .loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--md-sys-color-outline);
    border-top: 4px solid var(--md-sys-color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-screen h2 {
    color: var(--md-sys-color-error);
    margin-bottom: 16px;
  }

  .error-screen button {
    padding: 12px 24px;
    background: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    border: none;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 16px;
  }

  .error-screen button:hover {
    background: var(--md-sys-color-primary-container);
  }
</style>
