{"name": "ai-novel-assistant", "version": "1.0.0", "description": "AI驱动的智能小说创作工具", "main": "public/electron.js", "homepage": "./", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "electron-build": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist-win": "npm run build && electron-builder --win", "dist-mac": "npm run build && electron-builder --mac", "dist-linux": "npm run build && electron-builder --linux", "dist-all": "npm run build && electron-builder --win --mac --linux", "pack": "npm run build && electron-builder --dir", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint src --ext .js,.svelte", "lint:fix": "eslint src --ext .js,.svelte --fix", "format": "prettier --write src/**/*.{js,svelte,css,html}"}, "keywords": ["AI", "小说", "创作", "写作", "助手", "PWA", "Electron"], "author": "AI Novel Assistant Team", "license": "MIT", "dependencies": {"@material/web": "^1.0.0", "chart.js": "^4.4.0", "date-fns": "^3.0.0", "dompurify": "^3.0.0", "idb": "^8.0.0", "marked": "^12.0.0", "svelte": "^4.2.0", "svelte-routing": "^2.0.0"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^3.0.0", "@types/dompurify": "^3.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.0", "concurrently": "^8.0.0", "electron": "^28.0.0", "electron-builder": "^24.0.0", "eslint": "^8.0.0", "eslint-plugin-svelte": "^2.0.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "prettier-plugin-svelte": "^3.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.0", "vitest": "^1.0.0", "wait-on": "^7.0.0"}, "build": {"appId": "com.ainovel.assistant", "productName": "AI小说助手", "directories": {"output": "dist", "buildResources": "build"}, "files": ["build/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "resources/", "to": "resources/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "msi", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "build/icon.png", "category": "Office"}}}