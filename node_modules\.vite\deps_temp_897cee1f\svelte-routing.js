import "./chunk-X7HCJ7ZS.js";
import "./chunk-DYWWDCTU.js";
import {
  derived,
  writable
} from "./chunk-R4WBX7YJ.js";
import {
  SvelteComponentDev,
  add_location,
  add_render_callback,
  assign,
  check_outros,
  component_subscribe,
  compute_rest_props,
  construct_svelte_component_dev,
  createEventDispatcher,
  create_component,
  create_in_transition,
  create_out_transition,
  create_slot,
  destroy_component,
  detach_dev,
  dispatch_dev,
  element,
  empty,
  exclude_internal_props,
  getContext,
  get_all_dirty_from_scope,
  get_slot_changes,
  get_spread_object,
  get_spread_update,
  globals,
  group_outros,
  handle_promise,
  init,
  insert_dev,
  listen_dev,
  mount_component,
  noop,
  onDestroy,
  onMount,
  safe_not_equal,
  setContext,
  set_attributes,
  transition_in,
  transition_out,
  update_await_block_branch,
  update_slot_base,
  validate_slots,
  validate_store
} from "./chunk-MT4ZBHI2.js";
import "./chunk-672HPU4M.js";
import "./chunk-V6TY7KAL.js";

// node_modules/svelte-routing/src/contexts.js
var LOCATION = {};
var ROUTER = {};
var HISTORY = {};
var useLocation = () => getContext(LOCATION);
var useRouter = () => getContext(ROUTER);
var useHistory = () => getContext(HISTORY);

// node_modules/svelte-routing/src/utils.js
var PARAM = /^:(.+)/;
var SEGMENT_POINTS = 4;
var STATIC_POINTS = 3;
var DYNAMIC_POINTS = 2;
var SPLAT_PENALTY = 1;
var ROOT_POINTS = 1;
var segmentize = (uri) => uri.replace(/(^\/+|\/+$)/g, "").split("/");
var stripSlashes = (string) => string.replace(/(^\/+|\/+$)/g, "");
var rankRoute = (route, index) => {
  const score = route.default ? 0 : segmentize(route.path).reduce((score2, segment) => {
    score2 += SEGMENT_POINTS;
    if (segment === "") {
      score2 += ROOT_POINTS;
    } else if (PARAM.test(segment)) {
      score2 += DYNAMIC_POINTS;
    } else if (segment[0] === "*") {
      score2 -= SEGMENT_POINTS + SPLAT_PENALTY;
    } else {
      score2 += STATIC_POINTS;
    }
    return score2;
  }, 0);
  return { route, score, index };
};
var rankRoutes = (routes) => routes.map(rankRoute).sort(
  (a, b) => a.score < b.score ? 1 : a.score > b.score ? -1 : a.index - b.index
);
var pick = (routes, uri) => {
  let match;
  let default_;
  const [uriPathname] = uri.split("?");
  const uriSegments = segmentize(uriPathname);
  const isRootUri = uriSegments[0] === "";
  const ranked = rankRoutes(routes);
  for (let i = 0, l = ranked.length; i < l; i++) {
    const route = ranked[i].route;
    let missed = false;
    if (route.default) {
      default_ = {
        route,
        params: {},
        uri
      };
      continue;
    }
    const routeSegments = segmentize(route.path);
    const params = {};
    const max = Math.max(uriSegments.length, routeSegments.length);
    let index = 0;
    for (; index < max; index++) {
      const routeSegment = routeSegments[index];
      const uriSegment = uriSegments[index];
      if (routeSegment && routeSegment[0] === "*") {
        const splatName = routeSegment === "*" ? "*" : routeSegment.slice(1);
        params[splatName] = uriSegments.slice(index).map(decodeURIComponent).join("/");
        break;
      }
      if (typeof uriSegment === "undefined") {
        missed = true;
        break;
      }
      const dynamicMatch = PARAM.exec(routeSegment);
      if (dynamicMatch && !isRootUri) {
        const value = decodeURIComponent(uriSegment);
        params[dynamicMatch[1]] = value;
      } else if (routeSegment !== uriSegment) {
        missed = true;
        break;
      }
    }
    if (!missed) {
      match = {
        route,
        params,
        uri: "/" + uriSegments.slice(0, index).join("/")
      };
      break;
    }
  }
  return match || default_ || null;
};
var addQuery = (pathname, query) => pathname + (query ? `?${query}` : "");
var resolve = (to, base) => {
  if (to.startsWith("/")) return to;
  const [toPathname, toQuery] = to.split("?");
  const [basePathname] = base.split("?");
  const toSegments = segmentize(toPathname);
  const baseSegments = segmentize(basePathname);
  if (toSegments[0] === "") return addQuery(basePathname, toQuery);
  if (!toSegments[0].startsWith(".")) {
    const pathname = baseSegments.concat(toSegments).join("/");
    return addQuery((basePathname === "/" ? "" : "/") + pathname, toQuery);
  }
  const allSegments = baseSegments.concat(toSegments);
  const segments = [];
  allSegments.forEach((segment) => {
    if (segment === "..") segments.pop();
    else if (segment !== ".") segments.push(segment);
  });
  return addQuery("/" + segments.join("/"), toQuery);
};
var combinePaths = (basepath, path) => `${stripSlashes(
  path === "/" ? basepath : `${stripSlashes(basepath)}/${stripSlashes(path)}`
)}/`;
var shouldNavigate = (event) => !event.defaultPrevented && event.button === 0 && !(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);
var hostMatches = (anchor) => {
  const host = location.host;
  return anchor.host === host || anchor.href.indexOf(`https://${host}`) === 0 || anchor.href.indexOf(`http://${host}`) === 0;
};
var canUseDOM = () => typeof window !== "undefined" && "document" in window && "location" in window;

// node_modules/svelte-routing/src/Link.svelte
var file = "node_modules\\svelte-routing\\src\\Link.svelte";
var get_default_slot_changes = (dirty) => ({ active: dirty & /*ariaCurrent*/
4 });
var get_default_slot_context = (ctx) => ({ active: !!/*ariaCurrent*/
ctx[2] });
function create_fragment(ctx) {
  let a;
  let current;
  let mounted;
  let dispose;
  const default_slot_template = (
    /*#slots*/
    ctx[17].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[16],
    get_default_slot_context
  );
  let a_levels = [
    { href: (
      /*href*/
      ctx[0]
    ) },
    { "aria-current": (
      /*ariaCurrent*/
      ctx[2]
    ) },
    /*props*/
    ctx[1],
    /*$$restProps*/
    ctx[6]
  ];
  let a_data = {};
  for (let i = 0; i < a_levels.length; i += 1) {
    a_data = assign(a_data, a_levels[i]);
  }
  const block = {
    c: function create() {
      a = element("a");
      if (default_slot) default_slot.c();
      set_attributes(a, a_data);
      add_location(a, file, 41, 0, 1414);
    },
    l: function claim(nodes) {
      throw new Error("options.hydrate only works if the component was compiled with the `hydratable: true` option");
    },
    m: function mount(target, anchor) {
      insert_dev(target, a, anchor);
      if (default_slot) {
        default_slot.m(a, null);
      }
      current = true;
      if (!mounted) {
        dispose = listen_dev(
          a,
          "click",
          /*onClick*/
          ctx[5],
          false,
          false,
          false,
          false
        );
        mounted = true;
      }
    },
    p: function update(ctx2, [dirty]) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope, ariaCurrent*/
        65540)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[16],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[16]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[16],
              dirty,
              get_default_slot_changes
            ),
            get_default_slot_context
          );
        }
      }
      set_attributes(a, a_data = get_spread_update(a_levels, [
        (!current || dirty & /*href*/
        1) && { href: (
          /*href*/
          ctx2[0]
        ) },
        (!current || dirty & /*ariaCurrent*/
        4) && { "aria-current": (
          /*ariaCurrent*/
          ctx2[2]
        ) },
        dirty & /*props*/
        2 && /*props*/
        ctx2[1],
        dirty & /*$$restProps*/
        64 && /*$$restProps*/
        ctx2[6]
      ]));
    },
    i: function intro(local) {
      if (current) return;
      transition_in(default_slot, local);
      current = true;
    },
    o: function outro(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(a);
      }
      if (default_slot) default_slot.d(detaching);
      mounted = false;
      dispose();
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance($$self, $$props, $$invalidate) {
  let ariaCurrent;
  const omit_props_names = ["to", "replace", "state", "getProps", "preserveScroll"];
  let $$restProps = compute_rest_props($$props, omit_props_names);
  let $location;
  let $base;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Link", slots, ["default"]);
  let { to = "#" } = $$props;
  let { replace = false } = $$props;
  let { state = {} } = $$props;
  let { getProps = () => ({}) } = $$props;
  let { preserveScroll = false } = $$props;
  const location2 = getContext(LOCATION);
  validate_store(location2, "location");
  component_subscribe($$self, location2, (value) => $$invalidate(14, $location = value));
  const { base } = getContext(ROUTER);
  validate_store(base, "base");
  component_subscribe($$self, base, (value) => $$invalidate(15, $base = value));
  const { navigate: navigate2 } = getContext(HISTORY);
  const dispatch = createEventDispatcher();
  let href, isPartiallyCurrent, isCurrent, props;
  const onClick = (event) => {
    dispatch("click", event);
    if (shouldNavigate(event)) {
      event.preventDefault();
      const shouldReplace = $location.pathname === href || replace;
      navigate2(href, {
        state,
        replace: shouldReplace,
        preserveScroll
      });
    }
  };
  $$self.$$set = ($$new_props) => {
    $$props = assign(assign({}, $$props), exclude_internal_props($$new_props));
    $$invalidate(6, $$restProps = compute_rest_props($$props, omit_props_names));
    if ("to" in $$new_props) $$invalidate(7, to = $$new_props.to);
    if ("replace" in $$new_props) $$invalidate(8, replace = $$new_props.replace);
    if ("state" in $$new_props) $$invalidate(9, state = $$new_props.state);
    if ("getProps" in $$new_props) $$invalidate(10, getProps = $$new_props.getProps);
    if ("preserveScroll" in $$new_props) $$invalidate(11, preserveScroll = $$new_props.preserveScroll);
    if ("$$scope" in $$new_props) $$invalidate(16, $$scope = $$new_props.$$scope);
  };
  $$self.$capture_state = () => ({
    createEventDispatcher,
    getContext,
    HISTORY,
    LOCATION,
    ROUTER,
    resolve,
    shouldNavigate,
    to,
    replace,
    state,
    getProps,
    preserveScroll,
    location: location2,
    base,
    navigate: navigate2,
    dispatch,
    href,
    isPartiallyCurrent,
    isCurrent,
    props,
    onClick,
    ariaCurrent,
    $location,
    $base
  });
  $$self.$inject_state = ($$new_props) => {
    if ("to" in $$props) $$invalidate(7, to = $$new_props.to);
    if ("replace" in $$props) $$invalidate(8, replace = $$new_props.replace);
    if ("state" in $$props) $$invalidate(9, state = $$new_props.state);
    if ("getProps" in $$props) $$invalidate(10, getProps = $$new_props.getProps);
    if ("preserveScroll" in $$props) $$invalidate(11, preserveScroll = $$new_props.preserveScroll);
    if ("href" in $$props) $$invalidate(0, href = $$new_props.href);
    if ("isPartiallyCurrent" in $$props) $$invalidate(12, isPartiallyCurrent = $$new_props.isPartiallyCurrent);
    if ("isCurrent" in $$props) $$invalidate(13, isCurrent = $$new_props.isCurrent);
    if ("props" in $$props) $$invalidate(1, props = $$new_props.props);
    if ("ariaCurrent" in $$props) $$invalidate(2, ariaCurrent = $$new_props.ariaCurrent);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*to, $base*/
    32896) {
      $: $$invalidate(0, href = resolve(to, $base.uri));
    }
    if ($$self.$$.dirty & /*$location, href*/
    16385) {
      $: $$invalidate(12, isPartiallyCurrent = $location.pathname.startsWith(href));
    }
    if ($$self.$$.dirty & /*href, $location*/
    16385) {
      $: $$invalidate(13, isCurrent = href === $location.pathname);
    }
    if ($$self.$$.dirty & /*isCurrent*/
    8192) {
      $: $$invalidate(2, ariaCurrent = isCurrent ? "page" : void 0);
    }
    $: $$invalidate(1, props = getProps({
      location: $location,
      href,
      isPartiallyCurrent,
      isCurrent,
      existingProps: $$restProps
    }));
  };
  return [
    href,
    props,
    ariaCurrent,
    location2,
    base,
    onClick,
    $$restProps,
    to,
    replace,
    state,
    getProps,
    preserveScroll,
    isPartiallyCurrent,
    isCurrent,
    $location,
    $base,
    $$scope,
    slots
  ];
}
var Link = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance, create_fragment, safe_not_equal, {
      to: 7,
      replace: 8,
      state: 9,
      getProps: 10,
      preserveScroll: 11
    });
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Link",
      options,
      id: create_fragment.name
    });
  }
  get to() {
    throw new Error("<Link>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set to(value) {
    throw new Error("<Link>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get replace() {
    throw new Error("<Link>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set replace(value) {
    throw new Error("<Link>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get state() {
    throw new Error("<Link>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set state(value) {
    throw new Error("<Link>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get getProps() {
    throw new Error("<Link>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set getProps(value) {
    throw new Error("<Link>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get preserveScroll() {
    throw new Error("<Link>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set preserveScroll(value) {
    throw new Error("<Link>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Link_default = Link;

// node_modules/svelte-routing/src/Route.svelte
var get_default_slot_changes2 = (dirty) => ({ params: dirty & /*routeParams*/
4 });
var get_default_slot_context2 = (ctx) => ({ params: (
  /*routeParams*/
  ctx[2]
) });
function create_if_block(ctx) {
  let current_block_type_index;
  let if_block;
  let if_block_anchor;
  let current;
  const if_block_creators = [create_if_block_1, create_else_block];
  const if_blocks = [];
  function select_block_type(ctx2, dirty) {
    if (
      /*component*/
      ctx2[0]
    ) return 0;
    return 1;
  }
  current_block_type_index = select_block_type(ctx, -1);
  if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx);
  const block = {
    c: function create() {
      if_block.c();
      if_block_anchor = empty();
    },
    m: function mount(target, anchor) {
      if_blocks[current_block_type_index].m(target, anchor);
      insert_dev(target, if_block_anchor, anchor);
      current = true;
    },
    p: function update(ctx2, dirty) {
      let previous_block_index = current_block_type_index;
      current_block_type_index = select_block_type(ctx2, dirty);
      if (current_block_type_index === previous_block_index) {
        if_blocks[current_block_type_index].p(ctx2, dirty);
      } else {
        group_outros();
        transition_out(if_blocks[previous_block_index], 1, 1, () => {
          if_blocks[previous_block_index] = null;
        });
        check_outros();
        if_block = if_blocks[current_block_type_index];
        if (!if_block) {
          if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx2);
          if_block.c();
        } else {
          if_block.p(ctx2, dirty);
        }
        transition_in(if_block, 1);
        if_block.m(if_block_anchor.parentNode, if_block_anchor);
      }
    },
    i: function intro(local) {
      if (current) return;
      transition_in(if_block);
      current = true;
    },
    o: function outro(local) {
      transition_out(if_block);
      current = false;
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(if_block_anchor);
      }
      if_blocks[current_block_type_index].d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block.name,
    type: "if",
    source: "(42:0) {#if $activeRoute && $activeRoute.route === route}",
    ctx
  });
  return block;
}
function create_else_block(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[8].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[7],
    get_default_slot_context2
  );
  const block = {
    c: function create() {
      if (default_slot) default_slot.c();
    },
    m: function mount(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p: function update(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope, routeParams*/
        132)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[7],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[7]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[7],
              dirty,
              get_default_slot_changes2
            ),
            get_default_slot_context2
          );
        }
      }
    },
    i: function intro(local) {
      if (current) return;
      transition_in(default_slot, local);
      current = true;
    },
    o: function outro(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d: function destroy(detaching) {
      if (default_slot) default_slot.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_else_block.name,
    type: "else",
    source: "(51:4) {:else}",
    ctx
  });
  return block;
}
function create_if_block_1(ctx) {
  let await_block_anchor;
  let promise;
  let current;
  let info = {
    ctx,
    current: null,
    token: null,
    hasCatch: false,
    pending: create_pending_block,
    then: create_then_block,
    catch: create_catch_block,
    value: 12,
    blocks: [, , ,]
  };
  handle_promise(promise = /*component*/
  ctx[0], info);
  const block = {
    c: function create() {
      await_block_anchor = empty();
      info.block.c();
    },
    m: function mount(target, anchor) {
      insert_dev(target, await_block_anchor, anchor);
      info.block.m(target, info.anchor = anchor);
      info.mount = () => await_block_anchor.parentNode;
      info.anchor = await_block_anchor;
      current = true;
    },
    p: function update(new_ctx, dirty) {
      ctx = new_ctx;
      info.ctx = ctx;
      if (dirty & /*component*/
      1 && promise !== (promise = /*component*/
      ctx[0]) && handle_promise(promise, info)) {
      } else {
        update_await_block_branch(info, ctx, dirty);
      }
    },
    i: function intro(local) {
      if (current) return;
      transition_in(info.block);
      current = true;
    },
    o: function outro(local) {
      for (let i = 0; i < 3; i += 1) {
        const block2 = info.blocks[i];
        transition_out(block2);
      }
      current = false;
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(await_block_anchor);
      }
      info.block.d(detaching);
      info.token = null;
      info = null;
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_1.name,
    type: "if",
    source: "(43:4) {#if component}",
    ctx
  });
  return block;
}
function create_catch_block(ctx) {
  const block = {
    c: noop,
    m: noop,
    p: noop,
    i: noop,
    o: noop,
    d: noop
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_catch_block.name,
    type: "catch",
    source: "(1:0) <script>     import { getContext, onDestroy }",
    ctx
  });
  return block;
}
function create_then_block(ctx) {
  var _a;
  let switch_instance;
  let switch_instance_anchor;
  let current;
  const switch_instance_spread_levels = [
    /*routeParams*/
    ctx[2],
    /*routeProps*/
    ctx[3]
  ];
  var switch_value = (
    /*resolvedComponent*/
    ((_a = ctx[12]) == null ? void 0 : _a.default) || /*resolvedComponent*/
    ctx[12]
  );
  function switch_props(ctx2, dirty) {
    let switch_instance_props = {};
    for (let i = 0; i < switch_instance_spread_levels.length; i += 1) {
      switch_instance_props = assign(switch_instance_props, switch_instance_spread_levels[i]);
    }
    if (dirty !== void 0 && dirty & /*routeParams, routeProps*/
    12) {
      switch_instance_props = assign(switch_instance_props, get_spread_update(switch_instance_spread_levels, [
        dirty & /*routeParams*/
        4 && get_spread_object(
          /*routeParams*/
          ctx2[2]
        ),
        dirty & /*routeProps*/
        8 && get_spread_object(
          /*routeProps*/
          ctx2[3]
        )
      ]));
    }
    return {
      props: switch_instance_props,
      $$inline: true
    };
  }
  if (switch_value) {
    switch_instance = construct_svelte_component_dev(switch_value, switch_props(ctx));
  }
  const block = {
    c: function create() {
      if (switch_instance) create_component(switch_instance.$$.fragment);
      switch_instance_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (switch_instance) mount_component(switch_instance, target, anchor);
      insert_dev(target, switch_instance_anchor, anchor);
      current = true;
    },
    p: function update(ctx2, dirty) {
      var _a2;
      if (dirty & /*component*/
      1 && switch_value !== (switch_value = /*resolvedComponent*/
      ((_a2 = ctx2[12]) == null ? void 0 : _a2.default) || /*resolvedComponent*/
      ctx2[12])) {
        if (switch_instance) {
          group_outros();
          const old_component = switch_instance;
          transition_out(old_component.$$.fragment, 1, 0, () => {
            destroy_component(old_component, 1);
          });
          check_outros();
        }
        if (switch_value) {
          switch_instance = construct_svelte_component_dev(switch_value, switch_props(ctx2, dirty));
          create_component(switch_instance.$$.fragment);
          transition_in(switch_instance.$$.fragment, 1);
          mount_component(switch_instance, switch_instance_anchor.parentNode, switch_instance_anchor);
        } else {
          switch_instance = null;
        }
      } else if (switch_value) {
        const switch_instance_changes = dirty & /*routeParams, routeProps*/
        12 ? get_spread_update(switch_instance_spread_levels, [
          dirty & /*routeParams*/
          4 && get_spread_object(
            /*routeParams*/
            ctx2[2]
          ),
          dirty & /*routeProps*/
          8 && get_spread_object(
            /*routeProps*/
            ctx2[3]
          )
        ]) : {};
        switch_instance.$set(switch_instance_changes);
      }
    },
    i: function intro(local) {
      if (current) return;
      if (switch_instance) transition_in(switch_instance.$$.fragment, local);
      current = true;
    },
    o: function outro(local) {
      if (switch_instance) transition_out(switch_instance.$$.fragment, local);
      current = false;
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(switch_instance_anchor);
      }
      if (switch_instance) destroy_component(switch_instance, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_then_block.name,
    type: "then",
    source: "(44:49)              <svelte:component                 this={resolvedComponent?.default || resolvedComponent}",
    ctx
  });
  return block;
}
function create_pending_block(ctx) {
  const block = {
    c: noop,
    m: noop,
    p: noop,
    i: noop,
    o: noop,
    d: noop
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_pending_block.name,
    type: "pending",
    source: "(1:0) <script>     import { getContext, onDestroy }",
    ctx
  });
  return block;
}
function create_fragment2(ctx) {
  let if_block_anchor;
  let current;
  let if_block = (
    /*$activeRoute*/
    ctx[1] && /*$activeRoute*/
    ctx[1].route === /*route*/
    ctx[5] && create_if_block(ctx)
  );
  const block = {
    c: function create() {
      if (if_block) if_block.c();
      if_block_anchor = empty();
    },
    l: function claim(nodes) {
      throw new Error("options.hydrate only works if the component was compiled with the `hydratable: true` option");
    },
    m: function mount(target, anchor) {
      if (if_block) if_block.m(target, anchor);
      insert_dev(target, if_block_anchor, anchor);
      current = true;
    },
    p: function update(ctx2, [dirty]) {
      if (
        /*$activeRoute*/
        ctx2[1] && /*$activeRoute*/
        ctx2[1].route === /*route*/
        ctx2[5]
      ) {
        if (if_block) {
          if_block.p(ctx2, dirty);
          if (dirty & /*$activeRoute*/
          2) {
            transition_in(if_block, 1);
          }
        } else {
          if_block = create_if_block(ctx2);
          if_block.c();
          transition_in(if_block, 1);
          if_block.m(if_block_anchor.parentNode, if_block_anchor);
        }
      } else if (if_block) {
        group_outros();
        transition_out(if_block, 1, 1, () => {
          if_block = null;
        });
        check_outros();
      }
    },
    i: function intro(local) {
      if (current) return;
      transition_in(if_block);
      current = true;
    },
    o: function outro(local) {
      transition_out(if_block);
      current = false;
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(if_block_anchor);
      }
      if (if_block) if_block.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment2.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance2($$self, $$props, $$invalidate) {
  let $activeRoute;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Route", slots, ["default"]);
  let { path = "" } = $$props;
  let { component = null } = $$props;
  let routeParams = {};
  let routeProps = {};
  const { registerRoute, unregisterRoute, activeRoute } = getContext(ROUTER);
  validate_store(activeRoute, "activeRoute");
  component_subscribe($$self, activeRoute, (value) => $$invalidate(1, $activeRoute = value));
  const route = {
    path,
    // If no path prop is given, this Route will act as the default Route
    // that is rendered if no other Route in the Router is a match.
    default: path === ""
  };
  registerRoute(route);
  onDestroy(() => {
    unregisterRoute(route);
  });
  $$self.$$set = ($$new_props) => {
    $$invalidate(11, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("path" in $$new_props) $$invalidate(6, path = $$new_props.path);
    if ("component" in $$new_props) $$invalidate(0, component = $$new_props.component);
    if ("$$scope" in $$new_props) $$invalidate(7, $$scope = $$new_props.$$scope);
  };
  $$self.$capture_state = () => ({
    getContext,
    onDestroy,
    ROUTER,
    canUseDOM,
    path,
    component,
    routeParams,
    routeProps,
    registerRoute,
    unregisterRoute,
    activeRoute,
    route,
    $activeRoute
  });
  $$self.$inject_state = ($$new_props) => {
    $$invalidate(11, $$props = assign(assign({}, $$props), $$new_props));
    if ("path" in $$props) $$invalidate(6, path = $$new_props.path);
    if ("component" in $$props) $$invalidate(0, component = $$new_props.component);
    if ("routeParams" in $$props) $$invalidate(2, routeParams = $$new_props.routeParams);
    if ("routeProps" in $$props) $$invalidate(3, routeProps = $$new_props.routeProps);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    $: if ($activeRoute && $activeRoute.route === route) {
      $$invalidate(2, routeParams = $activeRoute.params);
      const { component: c, path: path2, ...rest } = $$props;
      $$invalidate(3, routeProps = rest);
      if (c) {
        if (c.toString().startsWith("class ")) $$invalidate(0, component = c);
        else $$invalidate(0, component = c());
      }
      canUseDOM() && !$activeRoute.preserveScroll && (window == null ? void 0 : window.scrollTo(0, 0));
    }
  };
  $$props = exclude_internal_props($$props);
  return [
    component,
    $activeRoute,
    routeParams,
    routeProps,
    activeRoute,
    route,
    path,
    $$scope,
    slots
  ];
}
var Route = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance2, create_fragment2, safe_not_equal, { path: 6, component: 0 });
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Route",
      options,
      id: create_fragment2.name
    });
  }
  get path() {
    throw new Error("<Route>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set path(value) {
    throw new Error("<Route>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get component() {
    throw new Error("<Route>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set component(value) {
    throw new Error("<Route>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Route_default = Route;

// node_modules/svelte-routing/src/history.js
var getLocation = (source) => {
  return {
    ...source.location,
    state: source.history.state,
    key: source.history.state && source.history.state.key || "initial"
  };
};
var createHistory = (source) => {
  const listeners = [];
  let location2 = getLocation(source);
  return {
    get location() {
      return location2;
    },
    listen(listener) {
      listeners.push(listener);
      const popstateListener = () => {
        location2 = getLocation(source);
        listener({ location: location2, action: "POP" });
      };
      source.addEventListener("popstate", popstateListener);
      return () => {
        source.removeEventListener("popstate", popstateListener);
        const index = listeners.indexOf(listener);
        listeners.splice(index, 1);
      };
    },
    navigate(to, { state, replace = false, preserveScroll = false, blurActiveElement = true } = {}) {
      state = { ...state, key: Date.now() + "" };
      try {
        if (replace) source.history.replaceState(state, "", to);
        else source.history.pushState(state, "", to);
      } catch (e) {
        source.location[replace ? "replace" : "assign"](to);
      }
      location2 = getLocation(source);
      listeners.forEach(
        (listener) => listener({ location: location2, action: "PUSH", preserveScroll })
      );
      if (blurActiveElement) document.activeElement.blur();
    }
  };
};
var createMemorySource = (initialPathname = "/") => {
  let index = 0;
  const stack = [{ pathname: initialPathname, search: "" }];
  const states = [];
  return {
    get location() {
      return stack[index];
    },
    addEventListener(name, fn) {
    },
    removeEventListener(name, fn) {
    },
    history: {
      get entries() {
        return stack;
      },
      get index() {
        return index;
      },
      get state() {
        return states[index];
      },
      pushState(state, _, uri) {
        const [pathname, search = ""] = uri.split("?");
        index++;
        stack.push({ pathname, search });
        states.push(state);
      },
      replaceState(state, _, uri) {
        const [pathname, search = ""] = uri.split("?");
        stack[index] = { pathname, search };
        states[index] = state;
      }
    }
  };
};
var globalHistory = createHistory(
  canUseDOM() ? window : createMemorySource()
);
var { navigate } = globalHistory;

// node_modules/svelte-routing/src/Router.svelte
var { Object: Object_1 } = globals;
var file2 = "node_modules\\svelte-routing\\src\\Router.svelte";
var get_default_slot_changes_1 = (dirty) => ({
  route: dirty & /*$activeRoute*/
  4,
  location: dirty & /*$location*/
  2
});
var get_default_slot_context_1 = (ctx) => ({
  route: (
    /*$activeRoute*/
    ctx[2] && /*$activeRoute*/
    ctx[2].uri
  ),
  location: (
    /*$location*/
    ctx[1]
  )
});
var get_default_slot_changes3 = (dirty) => ({
  route: dirty & /*$activeRoute*/
  4,
  location: dirty & /*$location*/
  2
});
var get_default_slot_context3 = (ctx) => ({
  route: (
    /*$activeRoute*/
    ctx[2] && /*$activeRoute*/
    ctx[2].uri
  ),
  location: (
    /*$location*/
    ctx[1]
  )
});
function create_else_block2(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[15].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[14],
    get_default_slot_context_1
  );
  const block = {
    c: function create() {
      if (default_slot) default_slot.c();
    },
    m: function mount(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p: function update(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope, $activeRoute, $location*/
        16390)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[14],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[14]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[14],
              dirty,
              get_default_slot_changes_1
            ),
            get_default_slot_context_1
          );
        }
      }
    },
    i: function intro(local) {
      if (current) return;
      transition_in(default_slot, local);
      current = true;
    },
    o: function outro(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d: function destroy(detaching) {
      if (default_slot) default_slot.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_else_block2.name,
    type: "else",
    source: "(143:0) {:else}",
    ctx
  });
  return block;
}
function create_if_block2(ctx) {
  let previous_key = (
    /*$location*/
    ctx[1].pathname
  );
  let key_block_anchor;
  let current;
  let key_block = create_key_block(ctx);
  const block = {
    c: function create() {
      key_block.c();
      key_block_anchor = empty();
    },
    m: function mount(target, anchor) {
      key_block.m(target, anchor);
      insert_dev(target, key_block_anchor, anchor);
      current = true;
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*$location*/
      2 && safe_not_equal(previous_key, previous_key = /*$location*/
      ctx2[1].pathname)) {
        group_outros();
        transition_out(key_block, 1, 1, noop);
        check_outros();
        key_block = create_key_block(ctx2);
        key_block.c();
        transition_in(key_block, 1);
        key_block.m(key_block_anchor.parentNode, key_block_anchor);
      } else {
        key_block.p(ctx2, dirty);
      }
    },
    i: function intro(local) {
      if (current) return;
      transition_in(key_block);
      current = true;
    },
    o: function outro(local) {
      transition_out(key_block);
      current = false;
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(key_block_anchor);
      }
      key_block.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block2.name,
    type: "if",
    source: "(134:0) {#if viewtransition}",
    ctx
  });
  return block;
}
function create_key_block(ctx) {
  let div;
  let div_intro;
  let div_outro;
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[15].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[14],
    get_default_slot_context3
  );
  const block = {
    c: function create() {
      div = element("div");
      if (default_slot) default_slot.c();
      add_location(div, file2, 135, 8, 4659);
    },
    m: function mount(target, anchor) {
      insert_dev(target, div, anchor);
      if (default_slot) {
        default_slot.m(div, null);
      }
      current = true;
    },
    p: function update(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope, $activeRoute, $location*/
        16390)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[14],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[14]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[14],
              dirty,
              get_default_slot_changes3
            ),
            get_default_slot_context3
          );
        }
      }
    },
    i: function intro(local) {
      if (current) return;
      transition_in(default_slot, local);
      if (local) {
        add_render_callback(() => {
          if (!current) return;
          if (div_outro) div_outro.end(1);
          div_intro = create_in_transition(
            div,
            /*viewtransitionFn*/
            ctx[3],
            {}
          );
          div_intro.start();
        });
      }
      current = true;
    },
    o: function outro(local) {
      transition_out(default_slot, local);
      if (div_intro) div_intro.invalidate();
      if (local) {
        div_outro = create_out_transition(
          div,
          /*viewtransitionFn*/
          ctx[3],
          {}
        );
      }
      current = false;
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      if (default_slot) default_slot.d(detaching);
      if (detaching && div_outro) div_outro.end();
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_key_block.name,
    type: "key",
    source: "(135:4) {#key $location.pathname}",
    ctx
  });
  return block;
}
function create_fragment3(ctx) {
  let current_block_type_index;
  let if_block;
  let if_block_anchor;
  let current;
  const if_block_creators = [create_if_block2, create_else_block2];
  const if_blocks = [];
  function select_block_type(ctx2, dirty) {
    if (
      /*viewtransition*/
      ctx2[0]
    ) return 0;
    return 1;
  }
  current_block_type_index = select_block_type(ctx, -1);
  if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx);
  const block = {
    c: function create() {
      if_block.c();
      if_block_anchor = empty();
    },
    l: function claim(nodes) {
      throw new Error("options.hydrate only works if the component was compiled with the `hydratable: true` option");
    },
    m: function mount(target, anchor) {
      if_blocks[current_block_type_index].m(target, anchor);
      insert_dev(target, if_block_anchor, anchor);
      current = true;
    },
    p: function update(ctx2, [dirty]) {
      let previous_block_index = current_block_type_index;
      current_block_type_index = select_block_type(ctx2, dirty);
      if (current_block_type_index === previous_block_index) {
        if_blocks[current_block_type_index].p(ctx2, dirty);
      } else {
        group_outros();
        transition_out(if_blocks[previous_block_index], 1, 1, () => {
          if_blocks[previous_block_index] = null;
        });
        check_outros();
        if_block = if_blocks[current_block_type_index];
        if (!if_block) {
          if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx2);
          if_block.c();
        } else {
          if_block.p(ctx2, dirty);
        }
        transition_in(if_block, 1);
        if_block.m(if_block_anchor.parentNode, if_block_anchor);
      }
    },
    i: function intro(local) {
      if (current) return;
      transition_in(if_block);
      current = true;
    },
    o: function outro(local) {
      transition_out(if_block);
      current = false;
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(if_block_anchor);
      }
      if_blocks[current_block_type_index].d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment3.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance3($$self, $$props, $$invalidate) {
  let $location;
  let $routes;
  let $base;
  let $activeRoute;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Router", slots, ["default"]);
  let { basepath = "/" } = $$props;
  let { url = null } = $$props;
  let { viewtransition = null } = $$props;
  let { history = globalHistory } = $$props;
  const viewtransitionFn = (node, _, direction) => {
    const vt = viewtransition(direction);
    if (typeof (vt == null ? void 0 : vt.fn) === "function") return vt.fn(node, vt);
    else return vt;
  };
  setContext(HISTORY, history);
  const locationContext = getContext(LOCATION);
  const routerContext = getContext(ROUTER);
  const routes = writable([]);
  validate_store(routes, "routes");
  component_subscribe($$self, routes, (value) => $$invalidate(12, $routes = value));
  const activeRoute = writable(null);
  validate_store(activeRoute, "activeRoute");
  component_subscribe($$self, activeRoute, (value) => $$invalidate(2, $activeRoute = value));
  let hasActiveRoute = false;
  const location2 = locationContext || writable(url ? { pathname: url } : history.location);
  validate_store(location2, "location");
  component_subscribe($$self, location2, (value) => $$invalidate(1, $location = value));
  const base = routerContext ? routerContext.routerBase : writable({ path: basepath, uri: basepath });
  validate_store(base, "base");
  component_subscribe($$self, base, (value) => $$invalidate(13, $base = value));
  const routerBase = derived([base, activeRoute], ([base2, activeRoute2]) => {
    if (!activeRoute2) return base2;
    const { path: basepath2 } = base2;
    const { route, uri } = activeRoute2;
    const path = route.default ? basepath2 : route.path.replace(/\*.*$/, "");
    return { path, uri };
  });
  const registerRoute = (route) => {
    const { path: basepath2 } = $base;
    let { path } = route;
    route._path = path;
    route.path = combinePaths(basepath2, path);
    if (typeof window === "undefined") {
      if (hasActiveRoute) return;
      const matchingRoute = pick([route], $location.pathname);
      if (matchingRoute) {
        activeRoute.set(matchingRoute);
        hasActiveRoute = true;
      }
    } else {
      routes.update((rs) => [...rs, route]);
    }
  };
  const unregisterRoute = (route) => {
    routes.update((rs) => rs.filter((r) => r !== route));
  };
  let preserveScroll = false;
  if (!locationContext) {
    onMount(() => {
      const unlisten = history.listen((event) => {
        $$invalidate(11, preserveScroll = event.preserveScroll || false);
        location2.set(event.location);
      });
      return unlisten;
    });
    setContext(LOCATION, location2);
  }
  setContext(ROUTER, {
    activeRoute,
    base,
    routerBase,
    registerRoute,
    unregisterRoute
  });
  const writable_props = ["basepath", "url", "viewtransition", "history"];
  Object_1.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot") console.warn(`<Router> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("basepath" in $$props2) $$invalidate(8, basepath = $$props2.basepath);
    if ("url" in $$props2) $$invalidate(9, url = $$props2.url);
    if ("viewtransition" in $$props2) $$invalidate(0, viewtransition = $$props2.viewtransition);
    if ("history" in $$props2) $$invalidate(10, history = $$props2.history);
    if ("$$scope" in $$props2) $$invalidate(14, $$scope = $$props2.$$scope);
  };
  $$self.$capture_state = () => ({
    getContext,
    onMount,
    setContext,
    derived,
    writable,
    HISTORY,
    LOCATION,
    ROUTER,
    globalHistory,
    combinePaths,
    pick,
    basepath,
    url,
    viewtransition,
    history,
    viewtransitionFn,
    locationContext,
    routerContext,
    routes,
    activeRoute,
    hasActiveRoute,
    location: location2,
    base,
    routerBase,
    registerRoute,
    unregisterRoute,
    preserveScroll,
    $location,
    $routes,
    $base,
    $activeRoute
  });
  $$self.$inject_state = ($$props2) => {
    if ("basepath" in $$props2) $$invalidate(8, basepath = $$props2.basepath);
    if ("url" in $$props2) $$invalidate(9, url = $$props2.url);
    if ("viewtransition" in $$props2) $$invalidate(0, viewtransition = $$props2.viewtransition);
    if ("history" in $$props2) $$invalidate(10, history = $$props2.history);
    if ("hasActiveRoute" in $$props2) hasActiveRoute = $$props2.hasActiveRoute;
    if ("preserveScroll" in $$props2) $$invalidate(11, preserveScroll = $$props2.preserveScroll);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*$base*/
    8192) {
      $: {
        const { path: basepath2 } = $base;
        routes.update((rs) => rs.map((r) => Object.assign(r, { path: combinePaths(basepath2, r._path) })));
      }
    }
    if ($$self.$$.dirty & /*$routes, $location, preserveScroll*/
    6146) {
      $: {
        const bestMatch = pick($routes, $location.pathname);
        activeRoute.set(bestMatch ? { ...bestMatch, preserveScroll } : bestMatch);
      }
    }
  };
  return [
    viewtransition,
    $location,
    $activeRoute,
    viewtransitionFn,
    routes,
    activeRoute,
    location2,
    base,
    basepath,
    url,
    history,
    preserveScroll,
    $routes,
    $base,
    $$scope,
    slots
  ];
}
var Router = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance3, create_fragment3, safe_not_equal, {
      basepath: 8,
      url: 9,
      viewtransition: 0,
      history: 10
    });
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Router",
      options,
      id: create_fragment3.name
    });
  }
  get basepath() {
    throw new Error("<Router>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set basepath(value) {
    throw new Error("<Router>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get url() {
    throw new Error("<Router>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set url(value) {
    throw new Error("<Router>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get viewtransition() {
    throw new Error("<Router>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set viewtransition(value) {
    throw new Error("<Router>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get history() {
    throw new Error("<Router>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set history(value) {
    throw new Error("<Router>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Router_default = Router;

// node_modules/svelte-routing/src/actions.js
var link = (node) => {
  const onClick = (event) => {
    const anchor = event.currentTarget;
    if ((anchor.target === "" || anchor.target === "_self") && hostMatches(anchor) && shouldNavigate(event)) {
      event.preventDefault();
      navigate(anchor.pathname + anchor.search, {
        replace: anchor.hasAttribute("replace"),
        preserveScroll: anchor.hasAttribute("preserveScroll")
      });
    }
  };
  node.addEventListener("click", onClick);
  return {
    destroy() {
      node.removeEventListener("click", onClick);
    }
  };
};
var links = (node) => {
  const findClosest = (tagName, el) => {
    while (el && el.tagName !== tagName) el = el.parentNode;
    return el;
  };
  const onClick = (event) => {
    const anchor = findClosest("A", event.target);
    if (anchor && (anchor.target === "" || anchor.target === "_self") && hostMatches(anchor) && shouldNavigate(event) && !anchor.hasAttribute("noroute")) {
      event.preventDefault();
      navigate(anchor.pathname + anchor.search, {
        replace: anchor.hasAttribute("replace"),
        preserveScroll: anchor.hasAttribute("preserveScroll")
      });
    }
  };
  node.addEventListener("click", onClick);
  return {
    destroy() {
      node.removeEventListener("click", onClick);
    }
  };
};
export {
  HISTORY,
  LOCATION,
  Link_default as Link,
  ROUTER,
  Route_default as Route,
  Router_default as Router,
  link,
  links,
  navigate,
  useHistory,
  useLocation,
  useRouter
};
//# sourceMappingURL=svelte-routing.js.map
