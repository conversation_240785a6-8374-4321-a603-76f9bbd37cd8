<execution>
  <constraint>
    ## 技术约束
    - **Svelte版本**：必须使用Svelte 4.x，确保与项目依赖兼容
    - **浏览器支持**：Chrome 90+, Firefox 88+, Safari 14+
    - **性能指标**：首屏加载<3秒，操作响应<200ms，内存使用<100MB
    - **包大小限制**：应用总大小<10MB，单个chunk<2MB
    - **PWA要求**：必须支持离线使用，通过Lighthouse PWA审核

    ## 设计约束
    - **Material Design 3.0**：严格遵循MD3设计规范和组件标准
    - **响应式设计**：支持1024px+桌面端和768px-移动端
    - **可访问性**：符合WCAG 2.1 AA级标准
    - **主题系统**：支持明亮主题，预留暗色主题扩展能力
  </constraint>

  <rule>
    ## 开发规则
    - **组件命名**：使用PascalCase，文件名与组件名一致
    - **状态管理**：优先使用Svelte内置store，复杂状态考虑模块化
    - **类型安全**：所有组件必须使用TypeScript，定义清晰的接口
    - **性能优先**：每个组件都要考虑性能影响，避免不必要的重渲染
    - **测试覆盖**：核心组件必须有单元测试，覆盖率>80%

    ## 代码规范
    - **ESLint配置**：使用@typescript-eslint/recommended规则集
    - **Prettier格式化**：统一代码格式，2空格缩进，单引号
    - **导入顺序**：第三方库 → 内部模块 → 相对路径导入
    - **注释规范**：复杂逻辑必须有注释，组件必须有JSDoc文档
  </rule>

  <guideline>
    ## 开发指导原则
    - **渐进增强**：确保基础功能在所有环境下可用
    - **组件复用**：优先复用现有组件，避免重复开发
    - **性能监控**：使用Performance API监控关键指标
    - **用户反馈**：所有异步操作都要有加载状态和错误处理
    - **移动优先**：优先考虑移动端体验，再适配桌面端

    ## Material Design实施指南
    - **颜色系统**：使用MD3颜色令牌，支持动态颜色
    - **字体系统**：使用Inter字体，定义清晰的字体层级
    - **间距系统**：使用8px基础间距，保持一致的视觉节奏
    - **阴影系统**：使用MD3阴影规范，营造层次感
    - **动画系统**：使用缓动函数，动画时长遵循MD3标准
  </guideline>

  <process>
    ## Svelte开发流程
    
    ### 1. 组件开发流程
    ```mermaid
    flowchart TD
        A[需求分析] --> B[组件设计]
        B --> C[接口定义]
        C --> D[实现开发]
        D --> E[单元测试]
        E --> F[集成测试]
        F --> G[性能测试]
        G --> H[代码审查]
        H --> I[文档更新]
    ```

    ### 2. 页面开发流程
    ```mermaid
    flowchart TD
        A[UI设计稿] --> B[组件拆分]
        B --> C[路由配置]
        C --> D[状态设计]
        D --> E[组件实现]
        E --> F[数据集成]
        F --> G[响应式适配]
        G --> H[性能优化]
        H --> I[用户测试]
    ```

    ### 3. PWA开发流程
    ```mermaid
    flowchart TD
        A[Service Worker配置] --> B[缓存策略设计]
        B --> C[离线页面开发]
        C --> D[Manifest配置]
        D --> E[安装体验优化]
        E --> F[更新机制实现]
        F --> G[PWA测试]
        G --> H[性能审核]
    ```

    ## 质量保证流程
    
    ### 代码质量检查
    - **静态分析**：ESLint + TypeScript编译检查
    - **格式检查**：Prettier格式化验证
    - **测试覆盖**：Jest单元测试 + Playwright E2E测试
    - **性能检查**：Lighthouse性能审核
    - **可访问性检查**：axe-core可访问性测试

    ### 发布前检查清单
    - [ ] 所有TypeScript错误已修复
    - [ ] ESLint检查通过，无警告
    - [ ] 单元测试覆盖率>80%
    - [ ] E2E测试全部通过
    - [ ] Lighthouse性能评分>90
    - [ ] PWA审核通过
    - [ ] 响应式设计在所有断点正常
    - [ ] 可访问性测试通过
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 性能指标
    - ✅ 首屏加载时间 < 3秒
    - ✅ 操作响应时间 < 200ms
    - ✅ 内存使用 < 100MB
    - ✅ Bundle大小 < 10MB
    - ✅ Lighthouse性能评分 > 90

    ### 代码质量
    - ✅ TypeScript覆盖率 100%
    - ✅ ESLint检查 0 errors, 0 warnings
    - ✅ 单元测试覆盖率 > 80%
    - ✅ 组件复用率 > 70%
    - ✅ 代码重复率 < 5%

    ### 用户体验
    - ✅ 响应式设计完美适配
    - ✅ 加载状态和错误处理完整
    - ✅ 操作反馈及时准确
    - ✅ 可访问性符合WCAG 2.1 AA
    - ✅ PWA功能完整可用

    ### Material Design合规性
    - ✅ 颜色系统符合MD3规范
    - ✅ 字体系统层级清晰
    - ✅ 间距系统一致规范
    - ✅ 组件行为符合MD3标准
    - ✅ 动画效果流畅自然
  </criteria>
</execution>
