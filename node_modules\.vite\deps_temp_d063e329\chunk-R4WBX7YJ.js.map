{"version": 3, "sources": ["../../svelte/src/runtime/store/index.js"], "sourcesContent": ["import {\n\trun_all,\n\tsubscribe,\n\tnoop,\n\tsafe_not_equal,\n\tis_function,\n\tget_store_value\n} from '../internal/index.js';\n\nconst subscriber_queue = [];\n\n/**\n * Creates a `Readable` store that allows reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#readable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readable(value, start) {\n\treturn {\n\t\tsubscribe: writable(value, start).subscribe\n\t};\n}\n\n/**\n * Create a `Writable` store that allows both updating and reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#writable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Writable<T>}\n */\nexport function writable(value, start = noop) {\n\t/** @type {import('./public.js').Unsubscriber} */\n\tlet stop;\n\t/** @type {Set<import('./private.js').SubscribeInvalidateTuple<T>>} */\n\tconst subscribers = new Set();\n\t/** @param {T} new_value\n\t * @returns {void}\n\t */\n\tfunction set(new_value) {\n\t\tif (safe_not_equal(value, new_value)) {\n\t\t\tvalue = new_value;\n\t\t\tif (stop) {\n\t\t\t\t// store is ready\n\t\t\t\tconst run_queue = !subscriber_queue.length;\n\t\t\t\tfor (const subscriber of subscribers) {\n\t\t\t\t\tsubscriber[1]();\n\t\t\t\t\tsubscriber_queue.push(subscriber, value);\n\t\t\t\t}\n\t\t\t\tif (run_queue) {\n\t\t\t\t\tfor (let i = 0; i < subscriber_queue.length; i += 2) {\n\t\t\t\t\t\tsubscriber_queue[i][0](subscriber_queue[i + 1]);\n\t\t\t\t\t}\n\t\t\t\t\tsubscriber_queue.length = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @param {import('./public.js').Updater<T>} fn\n\t * @returns {void}\n\t */\n\tfunction update(fn) {\n\t\tset(fn(value));\n\t}\n\n\t/**\n\t * @param {import('./public.js').Subscriber<T>} run\n\t * @param {import('./private.js').Invalidator<T>} [invalidate]\n\t * @returns {import('./public.js').Unsubscriber}\n\t */\n\tfunction subscribe(run, invalidate = noop) {\n\t\t/** @type {import('./private.js').SubscribeInvalidateTuple<T>} */\n\t\tconst subscriber = [run, invalidate];\n\t\tsubscribers.add(subscriber);\n\t\tif (subscribers.size === 1) {\n\t\t\tstop = start(set, update) || noop;\n\t\t}\n\t\trun(value);\n\t\treturn () => {\n\t\t\tsubscribers.delete(subscriber);\n\t\t\tif (subscribers.size === 0 && stop) {\n\t\t\t\tstop();\n\t\t\t\tstop = null;\n\t\t\t}\n\t\t};\n\t}\n\treturn { set, update, subscribe };\n}\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>, set: (value: T) => void, update: (fn: import('./public.js').Updater<T>) => void) => import('./public.js').Unsubscriber | void} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>) => T} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * @template {import('./private.js').Stores} S\n * @template T\n * @param {S} stores\n * @param {Function} fn\n * @param {T} [initial_value]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function derived(stores, fn, initial_value) {\n\tconst single = !Array.isArray(stores);\n\t/** @type {Array<import('./public.js').Readable<any>>} */\n\tconst stores_array = single ? [stores] : stores;\n\tif (!stores_array.every(Boolean)) {\n\t\tthrow new Error('derived() expects stores as input, got a falsy value');\n\t}\n\tconst auto = fn.length < 2;\n\treturn readable(initial_value, (set, update) => {\n\t\tlet started = false;\n\t\tconst values = [];\n\t\tlet pending = 0;\n\t\tlet cleanup = noop;\n\t\tconst sync = () => {\n\t\t\tif (pending) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tcleanup();\n\t\t\tconst result = fn(single ? values[0] : values, set, update);\n\t\t\tif (auto) {\n\t\t\t\tset(result);\n\t\t\t} else {\n\t\t\t\tcleanup = is_function(result) ? result : noop;\n\t\t\t}\n\t\t};\n\t\tconst unsubscribers = stores_array.map((store, i) =>\n\t\t\tsubscribe(\n\t\t\t\tstore,\n\t\t\t\t(value) => {\n\t\t\t\t\tvalues[i] = value;\n\t\t\t\t\tpending &= ~(1 << i);\n\t\t\t\t\tif (started) {\n\t\t\t\t\t\tsync();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t() => {\n\t\t\t\t\tpending |= 1 << i;\n\t\t\t\t}\n\t\t\t)\n\t\t);\n\t\tstarted = true;\n\t\tsync();\n\t\treturn function stop() {\n\t\t\trun_all(unsubscribers);\n\t\t\tcleanup();\n\t\t\t// We need to set this to false because callbacks can still happen despite having unsubscribed:\n\t\t\t// Callbacks might already be placed in the queue which doesn't know it should no longer\n\t\t\t// invoke this derived store.\n\t\t\tstarted = false;\n\t\t};\n\t});\n}\n\n/**\n * Takes a store and returns a new one derived from the old one that is readable.\n *\n * https://svelte.dev/docs/svelte-store#readonly\n * @template T\n * @param {import('./public.js').Readable<T>} store  - store to make readonly\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readonly(store) {\n\treturn {\n\t\tsubscribe: store.subscribe.bind(store)\n\t};\n}\n\nexport { get_store_value as get };\n"], "mappings": ";;;;;;;;;AASA,IAAM,mBAAmB,CAAC;AAWnB,SAAS,SAAS,OAAO,OAAO;AACtC,SAAO;AAAA,IACN,WAAW,SAAS,OAAO,KAAK,EAAE;AAAA,EACnC;AACD;AAWO,SAAS,SAAS,OAAO,QAAQ,MAAM;AAE7C,MAAI;AAEJ,QAAM,cAAc,oBAAI,IAAI;AAI5B,WAAS,IAAI,WAAW;AACvB,QAAI,eAAe,OAAO,SAAS,GAAG;AACrC,cAAQ;AACR,UAAI,MAAM;AAET,cAAM,YAAY,CAAC,iBAAiB;AACpC,mBAAW,cAAc,aAAa;AACrC,qBAAW,CAAC,EAAE;AACd,2BAAiB,KAAK,YAAY,KAAK;AAAA,QACxC;AACA,YAAI,WAAW;AACd,mBAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK,GAAG;AACpD,6BAAiB,CAAC,EAAE,CAAC,EAAE,iBAAiB,IAAI,CAAC,CAAC;AAAA,UAC/C;AACA,2BAAiB,SAAS;AAAA,QAC3B;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAMA,WAAS,OAAO,IAAI;AACnB,QAAI,GAAG,KAAK,CAAC;AAAA,EACd;AAOA,WAASA,WAAU,KAAK,aAAa,MAAM;AAE1C,UAAM,aAAa,CAAC,KAAK,UAAU;AACnC,gBAAY,IAAI,UAAU;AAC1B,QAAI,YAAY,SAAS,GAAG;AAC3B,aAAO,MAAM,KAAK,MAAM,KAAK;AAAA,IAC9B;AACA,QAAI,KAAK;AACT,WAAO,MAAM;AACZ,kBAAY,OAAO,UAAU;AAC7B,UAAI,YAAY,SAAS,KAAK,MAAM;AACnC,aAAK;AACL,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACA,SAAO,EAAE,KAAK,QAAQ,WAAAA,WAAU;AACjC;AAsCO,SAAS,QAAQ,QAAQ,IAAI,eAAe;AAClD,QAAM,SAAS,CAAC,MAAM,QAAQ,MAAM;AAEpC,QAAM,eAAe,SAAS,CAAC,MAAM,IAAI;AACzC,MAAI,CAAC,aAAa,MAAM,OAAO,GAAG;AACjC,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACvE;AACA,QAAM,OAAO,GAAG,SAAS;AACzB,SAAO,SAAS,eAAe,CAAC,KAAK,WAAW;AAC/C,QAAI,UAAU;AACd,UAAM,SAAS,CAAC;AAChB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,UAAM,OAAO,MAAM;AAClB,UAAI,SAAS;AACZ;AAAA,MACD;AACA,cAAQ;AACR,YAAM,SAAS,GAAG,SAAS,OAAO,CAAC,IAAI,QAAQ,KAAK,MAAM;AAC1D,UAAI,MAAM;AACT,YAAI,MAAM;AAAA,MACX,OAAO;AACN,kBAAU,YAAY,MAAM,IAAI,SAAS;AAAA,MAC1C;AAAA,IACD;AACA,UAAM,gBAAgB,aAAa;AAAA,MAAI,CAAC,OAAO,MAC9C;AAAA,QACC;AAAA,QACA,CAAC,UAAU;AACV,iBAAO,CAAC,IAAI;AACZ,qBAAW,EAAE,KAAK;AAClB,cAAI,SAAS;AACZ,iBAAK;AAAA,UACN;AAAA,QACD;AAAA,QACA,MAAM;AACL,qBAAW,KAAK;AAAA,QACjB;AAAA,MACD;AAAA,IACD;AACA,cAAU;AACV,SAAK;AACL,WAAO,SAAS,OAAO;AACtB,cAAQ,aAAa;AACrB,cAAQ;AAIR,gBAAU;AAAA,IACX;AAAA,EACD,CAAC;AACF;AAUO,SAAS,SAAS,OAAO;AAC/B,SAAO;AAAA,IACN,WAAW,MAAM,UAAU,KAAK,KAAK;AAAA,EACtC;AACD;", "names": ["subscribe"]}