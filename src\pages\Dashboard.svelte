<script>
  import { onMount } from 'svelte';
  
  // 仪表盘数据
  let dashboardData = {
    totalNovels: 0,
    totalChapters: 0,
    totalWords: 0,
    recentActivity: [],
    quickActions: [
      {
        title: '新建小说',
        description: '开始创作一部新的小说',
        icon: 'add',
        action: 'new-novel',
        color: 'primary'
      },
      {
        title: '生成大纲',
        description: '为现有小说生成详细大纲',
        icon: 'outline',
        action: 'generate-outline',
        color: 'secondary'
      },
      {
        title: '章节编辑',
        description: '编辑和完善章节内容',
        icon: 'edit',
        action: 'edit-chapter',
        color: 'tertiary'
      },
      {
        title: 'AI聊天',
        description: '与AI助手讨论创作想法',
        icon: 'chat',
        action: 'ai-chat',
        color: 'primary'
      }
    ]
  };
  
  onMount(async () => {
    try {
      console.log('仪表盘加载开始');
      await loadDashboardData();
      console.log('仪表盘加载成功');
    } catch (error) {
      console.error('仪表盘加载失败:', error);
    }
  });
  
  async function loadDashboardData() {
    // 这里将来会从数据库加载实际数据
    // 现在使用模拟数据
    dashboardData = {
      ...dashboardData,
      totalNovels: 3,
      totalChapters: 25,
      totalWords: 125000,
      recentActivity: [
        {
          id: 1,
          type: 'chapter_created',
          title: '创建了新章节',
          description: '《星际征途》第12章',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
          id: 2,
          type: 'outline_generated',
          title: '生成了大纲',
          description: '《魔法学院》完整大纲',
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000)
        },
        {
          id: 3,
          type: 'novel_created',
          title: '创建了新小说',
          description: '《都市修仙传》',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      ]
    };
  }
  
  function handleQuickAction(action) {
    console.log('快速操作:', action);
    alert(`点击了: ${action}`);
  }
  
  function formatNumber(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万';
    }
    return num.toLocaleString();
  }
  
  function formatTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays}天前`;
    } else if (diffHours > 0) {
      return `${diffHours}小时前`;
    } else {
      return '刚刚';
    }
  }
</script>

<div class="dashboard">
  <div class="dashboard__header">
    <h1 class="dashboard__title">仪表盘</h1>
    <p class="dashboard__subtitle">欢迎回到AI小说助手，继续您的创作之旅</p>
  </div>

  <!-- 统计卡片 -->
  <div class="dashboard__stats">
    <div class="stat-card">
      <div class="stat-card__content">
        <div class="stat-card__icon">
          <span class="material-symbols-outlined">book</span>
        </div>
        <div class="stat-card__info">
          <div class="stat-card__value">{dashboardData.totalNovels}</div>
          <div class="stat-card__label">小说总数</div>
        </div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-card__content">
        <div class="stat-card__icon">
          <span class="material-symbols-outlined">article</span>
        </div>
        <div class="stat-card__info">
          <div class="stat-card__value">{dashboardData.totalChapters}</div>
          <div class="stat-card__label">章节总数</div>
        </div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-card__content">
        <div class="stat-card__icon">
          <span class="material-symbols-outlined">edit_note</span>
        </div>
        <div class="stat-card__info">
          <div class="stat-card__value">{formatNumber(dashboardData.totalWords)}</div>
          <div class="stat-card__label">总字数</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 快速操作 -->
  <div class="dashboard__section">
    <h2 class="section__title">快速操作</h2>
    <div class="quick-actions">
      {#each dashboardData.quickActions as action}
        <div class="quick-action-card" on:click={() => handleQuickAction(action.action)}>
          <div class="quick-action__content">
            <div class="quick-action__icon quick-action__icon--{action.color}">
              <span class="material-symbols-outlined">{action.icon}</span>
            </div>
            <div class="quick-action__info">
              <h3 class="quick-action__title">{action.title}</h3>
              <p class="quick-action__description">{action.description}</p>
            </div>
          </div>
        </div>
      {/each}
    </div>
  </div>

  <!-- 最近活动 -->
  <div class="dashboard__section">
    <h2 class="section__title">最近活动</h2>
    <div class="activity-card">
      <div class="activity-list">
        {#each dashboardData.recentActivity as activity}
          <div class="activity-item">
            <div class="activity-item__icon">
              <span class="material-symbols-outlined">
                {#if activity.type === 'chapter_created'}
                  article
                {:else if activity.type === 'outline_generated'}
                  outline
                {:else if activity.type === 'novel_created'}
                  book
                {:else}
                  event
                {/if}
              </span>
            </div>
            <div class="activity-item__content">
              <div class="activity-item__title">{activity.title}</div>
              <div class="activity-item__description">{activity.description}</div>
            </div>
            <div class="activity-item__time">
              {formatTimeAgo(activity.timestamp)}
            </div>
          </div>
        {/each}
      </div>
    </div>
  </div>
</div>

<style>
  .dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--md-sys-spacing-lg);
  }

  .dashboard__header {
    margin-bottom: var(--md-sys-spacing-xl);
  }

  .dashboard__title {
    font-family: var(--md-sys-typescale-headline-large-font);
    font-size: var(--md-sys-typescale-headline-large-size);
    font-weight: var(--md-sys-typescale-headline-large-weight);
    line-height: var(--md-sys-typescale-headline-large-line-height);
    color: var(--md-sys-color-on-background);
    margin-bottom: var(--md-sys-spacing-sm);
  }

  .dashboard__subtitle {
    font-family: var(--md-sys-typescale-body-large-font);
    font-size: var(--md-sys-typescale-body-large-size);
    color: var(--md-sys-color-on-surface-variant);
  }

  .dashboard__stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--md-sys-spacing-lg);
    margin-bottom: var(--md-sys-spacing-xl);
  }

  .dashboard__section {
    margin-bottom: var(--md-sys-spacing-xl);
  }

  .section__title {
    font-family: var(--md-sys-typescale-headline-medium-font);
    font-size: var(--md-sys-typescale-headline-medium-size);
    font-weight: var(--md-sys-typescale-headline-medium-weight);
    color: var(--md-sys-color-on-background);
    margin-bottom: var(--md-sys-spacing-lg);
  }

  /* 统计卡片样式 */
  .stat-card {
    padding: var(--md-sys-spacing-lg);
    background: var(--md-sys-color-surface-container-low);
    border-radius: var(--md-sys-shape-corner-md);
    box-shadow: var(--md-sys-elevation-level1);
  }

  .stat-card__content {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-md);
  }

  .stat-card__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: var(--md-sys-color-primary-container);
    color: var(--md-sys-color-on-primary-container);
    border-radius: var(--md-sys-shape-corner-md);
  }

  .stat-card__icon span {
    font-size: 24px;
  }

  .stat-card__value {
    font-family: var(--md-sys-typescale-headline-medium-font);
    font-size: var(--md-sys-typescale-headline-medium-size);
    font-weight: var(--md-sys-typescale-headline-medium-weight);
    color: var(--md-sys-color-on-surface);
  }

  .stat-card__label {
    font-family: var(--md-sys-typescale-body-medium-font);
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface-variant);
  }

  /* 快速操作样式 */
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--md-sys-spacing-lg);
  }

  .quick-action-card {
    padding: var(--md-sys-spacing-lg);
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--md-sys-color-surface-container-low);
    border-radius: var(--md-sys-shape-corner-md);
    box-shadow: var(--md-sys-elevation-level1);
  }

  .quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--md-sys-elevation-level3);
  }

  .quick-action__content {
    display: flex;
    align-items: flex-start;
    gap: var(--md-sys-spacing-md);
  }

  .quick-action__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--md-sys-shape-corner-md);
    flex-shrink: 0;
  }

  .quick-action__icon--primary {
    background: var(--md-sys-color-primary-container);
    color: var(--md-sys-color-on-primary-container);
  }

  .quick-action__icon--secondary {
    background: var(--md-sys-color-secondary-container);
    color: var(--md-sys-color-on-secondary-container);
  }

  .quick-action__icon--tertiary {
    background: var(--md-sys-color-tertiary-container);
    color: var(--md-sys-color-on-tertiary-container);
  }

  .quick-action__icon span {
    font-size: 20px;
  }

  .quick-action__title {
    font-family: var(--md-sys-typescale-title-medium-font);
    font-size: var(--md-sys-typescale-title-medium-size);
    font-weight: var(--md-sys-typescale-title-medium-weight);
    color: var(--md-sys-color-on-surface);
    margin-bottom: var(--md-sys-spacing-xs);
  }

  .quick-action__description {
    font-family: var(--md-sys-typescale-body-medium-font);
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface-variant);
  }

  /* 活动列表样式 */
  .activity-card {
    padding: 0;
    background: var(--md-sys-color-surface-container-low);
    border-radius: var(--md-sys-shape-corner-md);
    box-shadow: var(--md-sys-elevation-level1);
  }

  .activity-list {
    display: flex;
    flex-direction: column;
  }

  .activity-item {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-md);
    padding: var(--md-sys-spacing-md) var(--md-sys-spacing-lg);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
  }

  .activity-item:last-child {
    border-bottom: none;
  }

  .activity-item__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--md-sys-color-surface-container-high);
    color: var(--md-sys-color-on-surface-variant);
    border-radius: var(--md-sys-shape-corner-sm);
    flex-shrink: 0;
  }

  .activity-item__icon span {
    font-size: 16px;
  }

  .activity-item__content {
    flex: 1;
  }

  .activity-item__title {
    font-family: var(--md-sys-typescale-body-large-font);
    font-size: var(--md-sys-typescale-body-large-size);
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
    margin-bottom: 2px;
  }

  .activity-item__description {
    font-family: var(--md-sys-typescale-body-medium-font);
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface-variant);
  }

  .activity-item__time {
    font-family: var(--md-sys-typescale-label-medium-font);
    font-size: var(--md-sys-typescale-label-medium-size);
    color: var(--md-sys-color-on-surface-variant);
    flex-shrink: 0;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .dashboard {
      padding: var(--md-sys-spacing-md);
    }

    .dashboard__stats {
      grid-template-columns: 1fr;
    }

    .quick-actions {
      grid-template-columns: 1fr;
    }

    .activity-item {
      padding: var(--md-sys-spacing-sm) var(--md-sys-spacing-md);
    }

    .activity-item__content {
      min-width: 0;
    }

    .activity-item__title,
    .activity-item__description {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
