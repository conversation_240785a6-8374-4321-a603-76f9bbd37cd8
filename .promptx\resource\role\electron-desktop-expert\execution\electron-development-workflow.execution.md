<execution>
  <constraint>
    ## 技术约束
    - **Electron版本**：使用Electron 28+，确保安全性和性能
    - **Node.js版本**：Node.js 18+，支持最新的JavaScript特性
    - **打包工具**：使用electron-builder进行跨平台打包
    - **更新框架**：使用electron-updater实现自动更新
    - **安全配置**：必须启用contextIsolation，禁用nodeIntegration

    ## 性能约束
    - **启动时间**：应用启动时间<5秒
    - **内存占用**：运行时内存占用<200MB
    - **包体积**：安装包大小<150MB（Windows）、<200MB（macOS）
    - **响应时间**：界面操作响应时间<200ms
    - **CPU使用**：空闲时CPU使用率<5%

    ## 平台约束
    - **Windows**：支持Windows 10+，提供NSIS、MSI、便携版
    - **macOS**：支持macOS 10.15+，提供DMG、ZIP格式
    - **Linux**：支持主流发行版，提供DEB、RPM、AppImage
    - **代码签名**：Windows和macOS必须提供签名版本
    - **中文支持**：完整支持中文界面和中文文件名
  </constraint>

  <rule>
    ## 开发规则
    - **安全优先**：所有渲染进程必须启用上下文隔离
    - **进程分离**：主进程负责系统交互，渲染进程负责UI
    - **IPC规范**：使用contextBridge暴露安全的API接口
    - **错误处理**：完善的崩溃恢复和错误报告机制
    - **资源管理**：及时释放不用的资源，避免内存泄漏

    ## 打包规则
    - **版本管理**：使用语义化版本号，自动生成更新日志
    - **文件组织**：合理的目录结构和资源文件管理
    - **依赖优化**：移除开发依赖，压缩生产代码
    - **平台适配**：针对不同平台优化图标和配置
    - **签名验证**：确保所有发布版本都经过代码签名
  </rule>

  <guideline>
    ## 开发指导原则
    - **原生体验**：遵循各平台的设计规范和用户习惯
    - **性能优先**：优化启动速度和运行时性能
    - **安全可靠**：实施严格的安全策略和错误处理
    - **易于维护**：清晰的代码结构和完善的文档
    - **用户友好**：提供直观的安装和使用体验

    ## Electron最佳实践
    - **进程管理**：合理控制进程数量，避免资源浪费
    - **IPC优化**：减少进程间通信频率，批量处理数据
    - **内存管理**：定期清理缓存，监控内存使用
    - **安全配置**：使用最严格的安全设置
    - **更新策略**：提供平滑的自动更新体验
  </guideline>

  <process>
    ## Electron开发流程
    
    ### 1. 项目初始化和架构设计
    ```mermaid
    flowchart TD
        A[项目初始化] --> B[依赖安装]
        B --> C[进程架构设计]
        C --> D[IPC接口设计]
        D --> E[安全配置]
        E --> F[开发环境配置]
        F --> G[调试工具配置]
        G --> H[基础功能测试]
    ```

    ### 2. 桌面功能开发
    ```mermaid
    flowchart TD
        A[窗口管理] --> B[菜单系统]
        B --> C[系统托盘]
        C --> D[文件关联]
        D --> E[快捷键支持]
        E --> F[系统集成]
        F --> G[原生对话框]
        G --> H[功能测试]
    ```

    ### 3. 打包和分发
    ```mermaid
    flowchart TD
        A[打包配置] --> B[图标资源]
        B --> C[平台构建]
        C --> D[代码签名]
        D --> E[安装测试]
        E --> F[自动更新配置]
        F --> G[发布准备]
        G --> H[分发测试]
    ```

    ## 质量保证流程
    
    ### 功能测试
    - **基础功能**：窗口管理、菜单操作、文件处理
    - **系统集成**：文件关联、协议注册、系统通知
    - **跨平台测试**：在所有目标平台上测试功能
    - **性能测试**：启动时间、内存使用、响应速度
    - **安全测试**：权限检查、数据保护、漏洞扫描

    ### 发布前检查清单
    - [ ] 所有目标平台打包成功
    - [ ] 代码签名验证通过
    - [ ] 自动更新机制正常
    - [ ] 安全配置符合最佳实践
    - [ ] 性能指标满足要求
    - [ ] 中文支持完整
    - [ ] 文件关联工作正常
    - [ ] 安装卸载流程顺畅
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 功能完整性
    - ✅ 跨平台打包支持完整
    - ✅ 自动更新机制正常
    - ✅ 桌面集成功能完善
    - ✅ 文件关联工作正常
    - ✅ 系统托盘功能可用

    ### 性能指标
    - ✅ 启动时间 < 5秒
    - ✅ 内存占用 < 200MB
    - ✅ 包体积符合要求
    - ✅ 界面响应 < 200ms
    - ✅ CPU使用率合理

    ### 安全性
    - ✅ 上下文隔离已启用
    - ✅ Node.js集成已禁用
    - ✅ 代码签名验证通过
    - ✅ 权限控制严格
    - ✅ 安全审计通过

    ### 用户体验
    - ✅ 安装体验流畅
    - ✅ 界面符合平台规范
    - ✅ 操作直观易用
    - ✅ 错误处理友好
    - ✅ 更新体验无感知

    ### 可维护性
    - ✅ 代码结构清晰
    - ✅ 配置管理规范
    - ✅ 文档完整详细
    - ✅ 调试工具完善
    - ✅ 错误日志详细
  </criteria>
</execution>
