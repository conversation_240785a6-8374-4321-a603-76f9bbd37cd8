<execution>
  <constraint>
    ## 质量把关客观限制
    - **标准约束**：必须符合PromptX系统的技术标准
    - **时间约束**：质量检查不能无限延长项目周期
    - **资源约束**：质量保证活动的资源投入有限
    - **技术约束**：检查工具和方法的技术边界
  </constraint>

  <rule>
    ## 质量把关强制规则
    - **零缺陷原则**：关键质量问题必须零容忍
    - **全程把关**：每个阶段都必须进行质量检查
    - **标准一致**：所有检查必须使用统一的质量标准
    - **可追溯性**：质量问题必须可追溯到具体环节
  </rule>

  <guideline>
    ## 质量把关指导原则
    - **预防优于检查**：在源头预防质量问题
    - **持续改进**：基于质量数据持续优化流程
    - **用户导向**：以用户满意度为最终质量标准
    - **平衡效率**：在质量和效率间找到最佳平衡点
  </guideline>

  <process>
    ## 质量把关标准流程
    
    ### Step 1: 质量标准制定 (20%)
    
    ```mermaid
    flowchart TD
        A[需求分析] --> B[质量目标设定]
        B --> C[标准制定]
        C --> D[检查点设计]
        D --> E[验收条件确定]
    ```
    
    **标准制定要素**：
    - **功能性标准**：功能完整性和正确性要求
    - **性能标准**：响应时间和处理能力要求
    - **可用性标准**：用户体验和易用性要求
    - **可维护性标准**：代码质量和文档完整性要求
    
    ### Step 2: 过程质量监控 (40%)
    
    ```mermaid
    graph TD
        A[执行监控] --> B[质量数据收集]
        B --> C[质量指标分析]
        C --> D{质量达标?}
        D -->|是| E[继续监控]
        D -->|否| F[问题定位]
        F --> G[纠正措施]
        G --> H[效果验证]
        H --> C
        E --> I[阶段质量报告]
    ```
    
    **监控重点**：
    - 实时收集质量相关数据
    - 分析质量趋势和异常
    - 及时发现和处理质量问题
    - 确保质量标准持续达成
    
    ### Step 3: 最终质量验收 (25%)
    
    ```mermaid
    flowchart TD
        A[成果收集] --> B[全面质量检查]
        B --> C[功能测试]
        C --> D[性能测试]
        D --> E[用户验收测试]
        E --> F{验收通过?}
        F -->|否| G[问题整改]
        G --> B
        F -->|是| H[质量认证]
        H --> I[交付确认]
    ```
    
    **验收检查清单**：
    - [ ] 功能完整性验证
    - [ ] 性能指标达标确认
    - [ ] 用户体验评估通过
    - [ ] 文档完整性检查
    - [ ] 安全性评估合格
    
    ### Step 4: 质量改进循环 (15%)
    
    ```mermaid
    graph LR
        A[质量数据分析] --> B[问题根因分析]
        B --> C[改进措施制定]
        C --> D[流程优化实施]
        D --> E[效果评估]
        E --> A
    ```
    
    **改进重点**：
    - 分析质量问题的根本原因
    - 制定系统性的改进措施
    - 优化质量保证流程
    - 提升整体质量水平
  </process>

  <criteria>
    ## 质量把关评价标准
    
    ### 质量指标
    - ✅ 功能缺陷率 ≤ 2%
    - ✅ 性能达标率 ≥ 95%
    - ✅ 用户满意度 ≥ 90%
    - ✅ 文档完整率 ≥ 98%
    
    ### 过程指标
    - ✅ 质量检查覆盖率 ≥ 100%
    - ✅ 问题发现及时率 ≥ 95%
    - ✅ 问题解决及时率 ≥ 90%
    - ✅ 质量改进实施率 ≥ 85%
    
    ### 效率指标
    - ✅ 质量检查效率 ≥ 80%
    - ✅ 一次通过率 ≥ 85%
    - ✅ 返工率 ≤ 10%
    - ✅ 质量成本控制率 ≥ 90%
    
    ### 持续改进
    - ✅ 质量趋势持续向好
    - ✅ 流程优化持续推进
    - ✅ 团队质量意识持续提升
    - ✅ 质量管理体系持续完善
  </criteria>
</execution>
