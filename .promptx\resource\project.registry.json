{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-05T17:59:13.591Z", "updatedAt": "2025-08-05T17:59:13.677Z", "resourceCount": 35}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.598Z", "updatedAt": "2025-08-05T17:59:13.598Z", "scannedAt": "2025-08-05T17:59:13.598Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-workflow", "source": "project", "protocol": "execution", "name": "Ai Integration Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.601Z", "updatedAt": "2025-08-05T17:59:13.601Z", "scannedAt": "2025-08-05T17:59:13.601Z", "path": "role/ai-integration-expert/execution/ai-integration-workflow.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.604Z", "updatedAt": "2025-08-05T17:59:13.604Z", "scannedAt": "2025-08-05T17:59:13.604Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "database-architect", "source": "project", "protocol": "role", "name": "Database Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/database-architect/database-architect.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.606Z", "updatedAt": "2025-08-05T17:59:13.606Z", "scannedAt": "2025-08-05T17:59:13.606Z", "path": "role/database-architect/database-architect.role.md"}}, {"id": "database-design-workflow", "source": "project", "protocol": "execution", "name": "Database Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/database-architect/execution/database-design-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.608Z", "updatedAt": "2025-08-05T17:59:13.608Z", "scannedAt": "2025-08-05T17:59:13.608Z", "path": "role/database-architect/execution/database-design-workflow.execution.md"}}, {"id": "database-design-thinking", "source": "project", "protocol": "thought", "name": "Database Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/database-architect/thought/database-design-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.610Z", "updatedAt": "2025-08-05T17:59:13.610Z", "scannedAt": "2025-08-05T17:59:13.610Z", "path": "role/database-architect/thought/database-design-thinking.thought.md"}}, {"id": "documentation-expert", "source": "project", "protocol": "role", "name": "Documentation Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/documentation-expert/documentation-expert.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.613Z", "updatedAt": "2025-08-05T17:59:13.613Z", "scannedAt": "2025-08-05T17:59:13.613Z", "path": "role/documentation-expert/documentation-expert.role.md"}}, {"id": "ascii-drawing", "source": "project", "protocol": "execution", "name": "Ascii Drawing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/documentation-expert/execution/ascii-drawing.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.616Z", "updatedAt": "2025-08-05T17:59:13.616Z", "scannedAt": "2025-08-05T17:59:13.616Z", "path": "role/documentation-expert/execution/ascii-drawing.execution.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/documentation-expert/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.618Z", "updatedAt": "2025-08-05T17:59:13.618Z", "scannedAt": "2025-08-05T17:59:13.618Z", "path": "role/documentation-expert/execution/documentation-workflow.execution.md"}}, {"id": "ascii-visualization", "source": "project", "protocol": "thought", "name": "Ascii Visualization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/documentation-expert/thought/ascii-visualization.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.620Z", "updatedAt": "2025-08-05T17:59:13.620Z", "scannedAt": "2025-08-05T17:59:13.620Z", "path": "role/documentation-expert/thought/ascii-visualization.thought.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/documentation-expert/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.622Z", "updatedAt": "2025-08-05T17:59:13.622Z", "scannedAt": "2025-08-05T17:59:13.622Z", "path": "role/documentation-expert/thought/documentation-thinking.thought.md"}}, {"id": "electron-desktop-expert", "source": "project", "protocol": "role", "name": "Electron Desktop Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/electron-desktop-expert/electron-desktop-expert.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.624Z", "updatedAt": "2025-08-05T17:59:13.624Z", "scannedAt": "2025-08-05T17:59:13.624Z", "path": "role/electron-desktop-expert/electron-desktop-expert.role.md"}}, {"id": "electron-development-workflow", "source": "project", "protocol": "execution", "name": "Electron Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/electron-desktop-expert/execution/electron-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.627Z", "updatedAt": "2025-08-05T17:59:13.627Z", "scannedAt": "2025-08-05T17:59:13.627Z", "path": "role/electron-desktop-expert/execution/electron-development-workflow.execution.md"}}, {"id": "electron-development-thinking", "source": "project", "protocol": "thought", "name": "Electron Development Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/electron-desktop-expert/thought/electron-development-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.629Z", "updatedAt": "2025-08-05T17:59:13.629Z", "scannedAt": "2025-08-05T17:59:13.629Z", "path": "role/electron-desktop-expert/thought/electron-development-thinking.thought.md"}}, {"id": "novel-writing-expert", "source": "project", "protocol": "role", "name": "Novel Writing Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/novel-writing-expert/novel-writing-expert.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.631Z", "updatedAt": "2025-08-05T17:59:13.631Z", "scannedAt": "2025-08-05T17:59:13.631Z", "path": "role/novel-writing-expert/novel-writing-expert.role.md"}}, {"id": "outline-generator", "source": "project", "protocol": "role", "name": "Outline Generator 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/outline-generator/outline-generator.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.633Z", "updatedAt": "2025-08-05T17:59:13.633Z", "scannedAt": "2025-08-05T17:59:13.633Z", "path": "role/outline-generator/outline-generator.role.md"}}, {"id": "performance-optimizer", "source": "project", "protocol": "role", "name": "Performance Optimizer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/performance-optimizer/performance-optimizer.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.635Z", "updatedAt": "2025-08-05T17:59:13.635Z", "scannedAt": "2025-08-05T17:59:13.635Z", "path": "role/performance-optimizer/performance-optimizer.role.md"}}, {"id": "product-architect", "source": "project", "protocol": "role", "name": "Product Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/product-architect/product-architect.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.637Z", "updatedAt": "2025-08-05T17:59:13.637Z", "scannedAt": "2025-08-05T17:59:13.637Z", "path": "role/product-architect/product-architect.role.md"}}, {"id": "product-manager", "source": "project", "protocol": "role", "name": "Product Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/product-manager/product-manager.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.640Z", "updatedAt": "2025-08-05T17:59:13.640Z", "scannedAt": "2025-08-05T17:59:13.640Z", "path": "role/product-manager/product-manager.role.md"}}, {"id": "pyside6-ui-dev", "source": "project", "protocol": "role", "name": "Pyside6 Ui Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pyside6-ui-dev/pyside6-ui-dev.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.642Z", "updatedAt": "2025-08-05T17:59:13.642Z", "scannedAt": "2025-08-05T17:59:13.642Z", "path": "role/pyside6-ui-dev/pyside6-ui-dev.role.md"}}, {"id": "python-backend-workflow", "source": "project", "protocol": "execution", "name": "Python Backend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-backend-dev/execution/python-backend-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.645Z", "updatedAt": "2025-08-05T17:59:13.645Z", "scannedAt": "2025-08-05T17:59:13.645Z", "path": "role/python-backend-dev/execution/python-backend-workflow.execution.md"}}, {"id": "python-backend-dev", "source": "project", "protocol": "role", "name": "Python Backend Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-backend-dev/python-backend-dev.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.646Z", "updatedAt": "2025-08-05T17:59:13.646Z", "scannedAt": "2025-08-05T17:59:13.646Z", "path": "role/python-backend-dev/python-backend-dev.role.md"}}, {"id": "python-backend-thinking", "source": "project", "protocol": "thought", "name": "Python Backend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-backend-dev/thought/python-backend-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.649Z", "updatedAt": "2025-08-05T17:59:13.649Z", "scannedAt": "2025-08-05T17:59:13.649Z", "path": "role/python-backend-dev/thought/python-backend-thinking.thought.md"}}, {"id": "qa-engineer", "source": "project", "protocol": "role", "name": "Qa Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/qa-engineer/qa-engineer.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.652Z", "updatedAt": "2025-08-05T17:59:13.652Z", "scannedAt": "2025-08-05T17:59:13.652Z", "path": "role/qa-engineer/qa-engineer.role.md"}}, {"id": "svelte-development-workflow", "source": "project", "protocol": "execution", "name": "Svelte Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/svelte-frontend-expert/execution/svelte-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.655Z", "updatedAt": "2025-08-05T17:59:13.655Z", "scannedAt": "2025-08-05T17:59:13.655Z", "path": "role/svelte-frontend-expert/execution/svelte-development-workflow.execution.md"}}, {"id": "svelte-frontend-expert", "source": "project", "protocol": "role", "name": "Svelte Frontend Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/svelte-frontend-expert/svelte-frontend-expert.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.657Z", "updatedAt": "2025-08-05T17:59:13.657Z", "scannedAt": "2025-08-05T17:59:13.657Z", "path": "role/svelte-frontend-expert/svelte-frontend-expert.role.md"}}, {"id": "frontend-development-thinking", "source": "project", "protocol": "thought", "name": "Frontend Development Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/svelte-frontend-expert/thought/frontend-development-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.659Z", "updatedAt": "2025-08-05T17:59:13.659Z", "scannedAt": "2025-08-05T17:59:13.659Z", "path": "role/svelte-frontend-expert/thought/frontend-development-thinking.thought.md"}}, {"id": "project-coordination", "source": "project", "protocol": "execution", "name": "Project Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-coordination.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.663Z", "updatedAt": "2025-08-05T17:59:13.663Z", "scannedAt": "2025-08-05T17:59:13.663Z", "path": "role/system-director/execution/project-coordination.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.664Z", "updatedAt": "2025-08-05T17:59:13.664Z", "scannedAt": "2025-08-05T17:59:13.664Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.666Z", "updatedAt": "2025-08-05T17:59:13.666Z", "scannedAt": "2025-08-05T17:59:13.666Z", "path": "role/system-director/system-director.role.md"}}, {"id": "coordination-mindset", "source": "project", "protocol": "thought", "name": "Coordination Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/coordination-mindset.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.668Z", "updatedAt": "2025-08-05T17:59:13.668Z", "scannedAt": "2025-08-05T17:59:13.668Z", "path": "role/system-director/thought/coordination-mindset.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.669Z", "updatedAt": "2025-08-05T17:59:13.669Z", "scannedAt": "2025-08-05T17:59:13.669Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "ux-design-workflow", "source": "project", "protocol": "execution", "name": "Ux Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ux-designer/execution/ux-design-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T17:59:13.672Z", "updatedAt": "2025-08-05T17:59:13.672Z", "scannedAt": "2025-08-05T17:59:13.672Z", "path": "role/ux-designer/execution/ux-design-workflow.execution.md"}}, {"id": "ux-design-thinking", "source": "project", "protocol": "thought", "name": "Ux Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ux-designer/thought/ux-design-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T17:59:13.674Z", "updatedAt": "2025-08-05T17:59:13.674Z", "scannedAt": "2025-08-05T17:59:13.674Z", "path": "role/ux-designer/thought/ux-design-thinking.thought.md"}}, {"id": "ux-designer", "source": "project", "protocol": "role", "name": "Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ux-designer/ux-designer.role.md", "metadata": {"createdAt": "2025-08-05T17:59:13.676Z", "updatedAt": "2025-08-05T17:59:13.676Z", "scannedAt": "2025-08-05T17:59:13.676Z", "path": "role/ux-designer/ux-designer.role.md"}}], "stats": {"totalResources": 35, "byProtocol": {"role": 15, "execution": 10, "thought": 10}, "bySource": {"project": 35}}}