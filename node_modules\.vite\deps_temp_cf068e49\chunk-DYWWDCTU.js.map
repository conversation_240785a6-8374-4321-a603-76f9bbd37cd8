{"version": 3, "sources": ["../../svelte/src/runtime/internal/disclose-version/index.js"], "sourcesContent": ["import { PUBLIC_VERSION } from '../../../shared/version.js';\n\nif (typeof window !== 'undefined')\n\t// @ts-ignore\n\t(window.__svelte || (window.__svelte = { v: new Set() })).v.add(PUBLIC_VERSION);\n"], "mappings": ";;;;;AAEA,IAAI,OAAO,WAAW;AAErB,GAAC,OAAO,aAAa,OAAO,WAAW,EAAE,GAAG,oBAAI,IAAI,EAAE,IAAI,EAAE,IAAI,cAAc;", "names": []}