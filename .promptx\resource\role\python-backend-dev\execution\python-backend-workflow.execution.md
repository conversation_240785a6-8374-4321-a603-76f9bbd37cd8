<execution>
  <constraint>
    ## 技术约束条件
    - **Python版本**：必须兼容Python 3.8+，确保广泛的系统兼容性
    - **PySide6集成**：后端模块必须与PySide6界面层无缝协作
    - **内存限制**：桌面应用内存使用控制在合理范围内
    - **文件系统**：支持Windows、macOS、Linux的文件路径处理
    - **网络依赖**：AI服务调用需要处理网络异常和超时
  </constraint>

  <rule>
    ## 强制性开发规则
    - **代码规范**：严格遵循PEP 8编码规范，使用类型提示
    - **错误处理**：所有外部调用必须包含异常处理机制
    - **日志记录**：关键操作必须记录日志，便于问题排查
    - **单元测试**：核心业务逻辑必须编写对应的单元测试
    - **文档字符串**：所有公共方法必须包含详细的docstring
    - **依赖管理**：使用requirements.txt管理项目依赖
  </rule>

  <guideline>
    ## 开发指导原则
    - **SOLID原则**：遵循面向对象设计的SOLID原则
    - **DRY原则**：避免代码重复，提取公共逻辑
    - **KISS原则**：保持代码简单直观，避免过度复杂化
    - **渐进式开发**：先实现核心功能，再逐步完善细节
    - **用户体验优先**：技术实现服务于用户体验目标
    - **性能意识**：在设计阶段就考虑性能影响
  </guideline>

  <process>
    ## 标准开发流程
    
    ### Step 1: 需求分析与设计 (20%)
    ```mermaid
    flowchart TD
        A[需求分析] --> B[技术调研]
        B --> C[架构设计]
        C --> D[接口设计]
        D --> E[数据模型设计]
        E --> F[设计评审]
    ```
    
    **关键活动**：
    - 理解业务需求和用户场景
    - 评估技术可行性和风险
    - 设计系统架构和模块划分
    - 定义模块间接口和数据结构
    
    ### Step 2: 核心开发实现 (50%)
    ```mermaid
    flowchart TD
        A[环境搭建] --> B[基础框架]
        B --> C[数据模型]
        C --> D[业务逻辑]
        D --> E[接口实现]
        E --> F[集成测试]
    ```
    
    **开发顺序**：
    1. **数据模型层**：定义核心数据结构和序列化
    2. **管理器层**：实现业务逻辑管理器
    3. **服务层**：实现AI客户端和外部服务集成
    4. **工具层**：实现通用工具和辅助功能
    
    ### Step 3: 测试与优化 (20%)
    ```mermaid
    flowchart TD
        A[单元测试] --> B[集成测试]
        B --> C[性能测试]
        C --> D[错误处理测试]
        D --> E[用户验收测试]
        E --> F[性能优化]
    ```
    
    **测试策略**：
    - **单元测试**：覆盖核心业务逻辑
    - **集成测试**：验证模块间协作
    - **异常测试**：验证错误处理机制
    - **性能测试**：验证关键路径性能
    
    ### Step 4: 文档与交付 (10%)
    ```mermaid
    flowchart TD
        A[API文档] --> B[架构文档]
        B --> C[部署文档]
        C --> D[用户手册]
        D --> E[代码审查]
        E --> F[版本发布]
    ```
    
    ## 代码组织标准
    
    ### 目录结构规范
    ```
    ai_novel_assistant/
    ├── core/                 # 核心业务逻辑
    │   ├── __init__.py
    │   ├── models.py         # 数据模型
    │   ├── ai_client.py      # AI客户端管理
    │   ├── project_manager.py # 项目管理
    │   └── prompt_manager.py  # 提示词管理
    ├── utils/                # 工具模块
    │   ├── __init__.py
    │   ├── logger.py         # 日志工具
    │   ├── file_handler.py   # 文件处理
    │   └── ui_utils.py       # UI工具
    ├── config/               # 配置模块
    │   ├── __init__.py
    │   ├── settings.py       # 应用设置
    │   └── ui_config.py      # UI配置
    └── tests/                # 测试模块
        ├── __init__.py
        ├── test_models.py
        └── test_managers.py
    ```
    
    ### 模块设计模式
    
    #### 管理器模式
    ```python
    class BaseManager(LoggerMixin):
        """管理器基类"""
        def __init__(self):
            self.is_initialized = False
        
        def initialize(self):
            """初始化管理器"""
            pass
        
        def cleanup(self):
            """清理资源"""
            pass
    ```
    
    #### 客户端适配器模式
    ```python
    class BaseAIClient(ABC):
        """AI客户端基类"""
        @abstractmethod
        def generate(self, prompt: str, **kwargs) -> str:
            pass
        
        @abstractmethod
        def test_connection(self) -> bool:
            pass
    ```
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 代码质量指标
    - ✅ **代码规范性**：100% PEP 8合规
    - ✅ **类型提示覆盖率**：≥90%
    - ✅ **文档字符串覆盖率**：≥95%
    - ✅ **单元测试覆盖率**：≥80%
    - ✅ **代码复杂度**：圈复杂度≤10
    
    ### 性能指标
    - ✅ **启动时间**：应用启动≤3秒
    - ✅ **响应时间**：UI操作响应≤200ms
    - ✅ **内存使用**：稳定运行内存≤200MB
    - ✅ **文件操作**：项目保存/加载≤1秒
    
    ### 可靠性指标
    - ✅ **异常处理**：所有外部调用有异常处理
    - ✅ **数据完整性**：项目数据保存/加载无丢失
    - ✅ **错误恢复**：网络异常后能自动重试
    - ✅ **用户提示**：错误信息对用户友好
    
    ### 可维护性指标
    - ✅ **模块耦合度**：模块间低耦合高内聚
    - ✅ **代码可读性**：新开发者能快速理解
    - ✅ **扩展性**：新功能添加不影响现有功能
    - ✅ **文档完整性**：技术文档与代码同步
  </criteria>
</execution>
