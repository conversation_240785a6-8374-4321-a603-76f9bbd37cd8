<execution>
  <constraint>
    ## 设计约束
    - **Material Design 3.0**：严格遵循MD3设计规范和组件标准
    - **浏览器兼容**：支持Chrome 90+, Firefox 88+, Safari 14+
    - **响应式要求**：1024px+桌面端，768px-移动端完美适配
    - **性能指标**：界面渲染<200ms，动画流畅60fps
    - **可访问性**：符合WCAG 2.1 AA级标准

    ## 业务约束
    - **界面数量**：必须设计17个主要界面的完整布局
    - **布局模式**：左侧导航280px + 主内容区域的固定布局
    - **分栏支持**：支持40:60、30:70等灵活分栏比例
    - **主题系统**：明亮主题为主，预留暗色主题扩展
    - **个性化**：支持界面布局和颜色的个性化定制
  </constraint>

  <rule>
    ## 设计规则
    - **组件复用**：优先使用标准MD3组件，减少自定义组件
    - **视觉层次**：使用颜色、大小、间距建立清晰的视觉层次
    - **交互一致性**：相同功能的交互模式必须保持一致
    - **信息密度**：合理控制信息密度，避免界面过于拥挤
    - **错误预防**：通过设计预防用户操作错误

    ## 开发规则
    - **设计交付**：提供完整的设计规范和组件库
    - **标注规范**：详细的尺寸、间距、颜色标注
    - **切图规范**：提供多倍率图标和图片资源
    - **动效规范**：详细的动画时长、缓动函数说明
    - **适配规范**：不同屏幕尺寸的适配方案
  </rule>

  <guideline>
    ## 设计指导原则
    - **用户中心**：所有设计决策以用户需求和体验为中心
    - **简洁高效**：界面简洁直观，操作高效便捷
    - **视觉美观**：符合现代审美，提供愉悦的视觉体验
    - **功能完整**：确保所有功能都有合适的界面承载
    - **扩展性好**：设计系统支持未来功能的扩展

    ## Material Design实施指南
    - **颜色运用**：合理使用主色、辅助色、表面色、状态色
    - **字体层次**：建立清晰的字体层次和信息架构
    - **间距系统**：使用8px基础间距，保持视觉节奏
    - **组件使用**：正确使用MD3组件，保持设计一致性
    - **动效设计**：使用合适的过渡动画增强用户体验
  </guideline>

  <process>
    ## UI/UX设计开发流程
    
    ### 1. 用户研究和需求分析
    ```mermaid
    flowchart TD
        A[用户画像分析] --> B[使用场景研究]
        B --> C[竞品分析]
        C --> D[需求优先级]
        D --> E[设计目标制定]
        E --> F[成功指标定义]
        F --> G[设计策略]
        G --> H[项目规划]
    ```

    ### 2. 信息架构和交互设计
    ```mermaid
    flowchart TD
        A[功能梳理] --> B[信息分类]
        B --> C[导航设计]
        C --> D[页面结构]
        D --> E[交互流程]
        E --> F[线框图设计]
        F --> G[原型制作]
        G --> H[可用性测试]
    ```

    ### 3. 视觉设计和组件库
    ```mermaid
    flowchart TD
        A[视觉风格定义] --> B[颜色系统设计]
        B --> C[字体系统设计]
        C --> D[组件库设计]
        D --> E[图标设计]
        E --> F[界面设计]
        F --> G[动效设计]
        G --> H[设计规范]
    ```

    ## 质量保证流程
    
    ### 设计评审检查
    - **设计一致性**：检查所有界面的视觉和交互一致性
    - **规范符合性**：验证是否符合Material Design 3.0规范
    - **可用性评估**：评估界面的易用性和学习成本
    - **可访问性检查**：验证无障碍访问的支持情况
    - **性能影响评估**：评估设计对性能的影响

    ### 发布前检查清单
    - [ ] 17个界面设计全部完成
    - [ ] Material Design 3.0规范符合
    - [ ] 响应式设计适配完成
    - [ ] 组件库和设计规范完整
    - [ ] 可访问性标准达标
    - [ ] 动效设计合理流畅
    - [ ] 用户测试反馈良好
    - [ ] 开发交付文档完整
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 设计完整性
    - ✅ 17个主要界面设计完成
    - ✅ 完整的组件库和设计系统
    - ✅ 详细的设计规范文档
    - ✅ 响应式设计方案完整
    - ✅ 动效设计规范清晰

    ### 用户体验
    - ✅ 界面直观易用，学习成本低
    - ✅ 操作流程高效便捷
    - ✅ 视觉层次清晰合理
    - ✅ 错误预防和恢复机制完善
    - ✅ 个性化定制选项丰富

    ### 设计质量
    - ✅ 符合Material Design 3.0规范
    - ✅ 视觉设计美观现代
    - ✅ 交互设计一致性好
    - ✅ 信息架构清晰合理
    - ✅ 组件复用率高

    ### 技术实现
    - ✅ 设计可实现性强
    - ✅ 性能影响可控
    - ✅ 开发友好度高
    - ✅ 维护成本低
    - ✅ 扩展性好

    ### 可访问性
    - ✅ 符合WCAG 2.1 AA标准
    - ✅ 支持键盘导航
    - ✅ 屏幕阅读器友好
    - ✅ 高对比度模式支持
    - ✅ 色彩无障碍设计
  </criteria>
</execution>
