import { defineConfig } from 'vite';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import { VitePWA } from 'vite-plugin-pwa';

export default defineConfig({
  plugins: [
    svelte({
      compilerOptions: {
        dev: process.env.NODE_ENV !== 'production'
      },
      emitCss: true
    }),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\./,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 // 24小时
              }
            }
          }
        ]
      },
      manifest: {
        name: 'AI小说助手',
        short_name: 'AI小说助手',
        description: 'AI驱动的智能小说创作工具',
        start_url: '/',
        display: 'standalone',
        orientation: 'portrait-primary',
        theme_color: '#1976D2',
        background_color: '#ffffff',
        scope: '/',
        lang: 'zh-CN',
        categories: ['productivity', 'writing', 'creativity'],
        icons: [
          {
            src: '/icons/icon-72.png',
            sizes: '72x72',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-96.png',
            sizes: '96x96',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-128.png',
            sizes: '128x128',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-144.png',
            sizes: '144x144',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-152.png',
            sizes: '152x152',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-192.png',
            sizes: '192x192',
            type: 'image/png',
            purpose: 'any maskable'
          },
          {
            src: '/icons/icon-384.png',
            sizes: '384x384',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable'
          }
        ],
        shortcuts: [
          {
            name: '新建小说',
            short_name: '新建',
            description: '创建一部新的小说',
            url: '/new-novel',
            icons: [
              {
                src: '/icons/shortcut-new.png',
                sizes: '96x96'
              }
            ]
          },
          {
            name: '继续创作',
            short_name: '创作',
            description: '继续当前小说的创作',
            url: '/continue-writing',
            icons: [
              {
                src: '/icons/shortcut-write.png',
                sizes: '96x96'
              }
            ]
          }
        ]
      }
    })
  ],
  build: {
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info']
      },
      mangle: {
        safari10: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['svelte'],
          ai: ['src/services/ai'],
          storage: ['src/services/storage'],
          ui: ['src/components/ui'],
          charts: ['chart.js']
        }
      }
    },
    chunkSizeWarningLimit: 1000,
    assetsInlineLimit: 4096
  },
  optimizeDeps: {
    include: ['svelte', 'chart.js', 'date-fns']
  },
  server: {
    port: 5173,
    host: true
  }
});
