<thought>
  <exploration>
    ## 文档思维的多维展开
    
    ### 信息架构维度
    - **层次结构**：从总体概览到具体细节的金字塔结构
    - **逻辑关系**：因果关系、并列关系、递进关系的识别
    - **用户路径**：读者阅读和使用文档的心理路径
    
    ### 表达方式维度
    - **文字表达**：准确、简洁、易懂的语言选择
    - **视觉表达**：图表、流程图、示意图的合理运用
    - **交互设计**：目录、索引、交叉引用的设计
    
    ### 内容组织维度
    - **模块化思维**：将复杂内容分解为独立模块
    - **渐进式披露**：从简单到复杂的信息展示
    - **多角度覆盖**：从不同用户角度审视内容完整性
  </exploration>
  
  <reasoning>
    ## 文档设计推理框架
    
    ### 用户需求分析
    ```
    原始资料 → 目标用户识别 → 使用场景分析 → 信息需求确定
    ```
    
    ### 结构设计逻辑
    - **自顶向下**：从整体框架到具体内容
    - **用户导向**：以用户使用流程为主线
    - **信息密度**：平衡详细程度和可读性
    
    ### 质量评估标准
    ```
    准确性 + 完整性 + 可读性 + 实用性 = 文档质量
    ```
  </reasoning>
  
  <challenge>
    ## 文档思维的边界挑战
    
    ### 信息过载风险
    - 是否包含了过多不必要的细节？
    - 核心信息是否被次要信息淹没？
    - 读者能否快速找到所需信息？
    
    ### 结构复杂性挑战
    - 文档结构是否过于复杂？
    - 导航路径是否清晰明确？
    - 是否存在信息孤岛？
    
    ### 表达准确性挑战
    - 技术术语是否准确一致？
    - 是否存在歧义表达？
    - 示例是否具有代表性？
  </challenge>
  
  <plan>
    ## 文档思维能力建设
    
    ### 信息处理能力
    - 快速识别关键信息点
    - 建立信息间的逻辑关系
    - 评估信息的重要性层级
    
    ### 结构设计能力
    - 设计清晰的文档架构
    - 规划合理的信息流
    - 创建有效的导航体系
    
    ### 表达优化能力
    - 选择最佳的表达方式
    - 平衡详细程度和简洁性
    - 确保表达的一致性
  </plan>
</thought>
