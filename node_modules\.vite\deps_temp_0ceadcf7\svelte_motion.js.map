{"version": 3, "sources": ["../../svelte/src/runtime/motion/utils.js", "../../svelte/src/runtime/motion/spring.js", "../../svelte/src/runtime/motion/tweened.js"], "sourcesContent": ["/**\n * @param {any} obj\n * @returns {boolean}\n */\nexport function is_date(obj) {\n\treturn Object.prototype.toString.call(obj) === '[object Date]';\n}\n", "import { writable } from '../store/index.js';\nimport { loop, now } from '../internal/index.js';\nimport { is_date } from './utils.js';\n\n/**\n * @template T\n * @param {import('./private.js').TickContext<T>} ctx\n * @param {T} last_value\n * @param {T} current_value\n * @param {T} target_value\n * @returns {T}\n */\nfunction tick_spring(ctx, last_value, current_value, target_value) {\n\tif (typeof current_value === 'number' || is_date(current_value)) {\n\t\t// @ts-ignore\n\t\tconst delta = target_value - current_value;\n\t\t// @ts-ignore\n\t\tconst velocity = (current_value - last_value) / (ctx.dt || 1 / 60); // guard div by 0\n\t\tconst spring = ctx.opts.stiffness * delta;\n\t\tconst damper = ctx.opts.damping * velocity;\n\t\tconst acceleration = (spring - damper) * ctx.inv_mass;\n\t\tconst d = (velocity + acceleration) * ctx.dt;\n\t\tif (Math.abs(d) < ctx.opts.precision && Math.abs(delta) < ctx.opts.precision) {\n\t\t\treturn target_value; // settled\n\t\t} else {\n\t\t\tctx.settled = false; // signal loop to keep ticking\n\t\t\t// @ts-ignore\n\t\t\treturn is_date(current_value) ? new Date(current_value.getTime() + d) : current_value + d;\n\t\t}\n\t} else if (Array.isArray(current_value)) {\n\t\t// @ts-ignore\n\t\treturn current_value.map((_, i) =>\n\t\t\ttick_spring(ctx, last_value[i], current_value[i], target_value[i])\n\t\t);\n\t} else if (typeof current_value === 'object') {\n\t\tconst next_value = {};\n\t\tfor (const k in current_value) {\n\t\t\t// @ts-ignore\n\t\t\tnext_value[k] = tick_spring(ctx, last_value[k], current_value[k], target_value[k]);\n\t\t}\n\t\t// @ts-ignore\n\t\treturn next_value;\n\t} else {\n\t\tthrow new Error(`Cannot spring ${typeof current_value} values`);\n\t}\n}\n\n/**\n * The spring function in Svelte creates a store whose value is animated, with a motion that simulates the behavior of a spring. This means when the value changes, instead of transitioning at a steady rate, it \"bounces\" like a spring would, depending on the physics parameters provided. This adds a level of realism to the transitions and can enhance the user experience.\n *\n * https://svelte.dev/docs/svelte-motion#spring\n * @template [T=any]\n * @param {T} [value]\n * @param {import('./private.js').SpringOpts} [opts]\n * @returns {import('./public.js').Spring<T>}\n */\nexport function spring(value, opts = {}) {\n\tconst store = writable(value);\n\tconst { stiffness = 0.15, damping = 0.8, precision = 0.01 } = opts;\n\t/** @type {number} */\n\tlet last_time;\n\t/** @type {import('../internal/private.js').Task} */\n\tlet task;\n\t/** @type {object} */\n\tlet current_token;\n\t/** @type {T} */\n\tlet last_value = value;\n\t/** @type {T} */\n\tlet target_value = value;\n\tlet inv_mass = 1;\n\tlet inv_mass_recovery_rate = 0;\n\tlet cancel_task = false;\n\t/**\n\t * @param {T} new_value\n\t * @param {import('./private.js').SpringUpdateOpts} opts\n\t * @returns {Promise<void>}\n\t */\n\tfunction set(new_value, opts = {}) {\n\t\ttarget_value = new_value;\n\t\tconst token = (current_token = {});\n\t\tif (value == null || opts.hard || (spring.stiffness >= 1 && spring.damping >= 1)) {\n\t\t\tcancel_task = true; // cancel any running animation\n\t\t\tlast_time = now();\n\t\t\tlast_value = new_value;\n\t\t\tstore.set((value = target_value));\n\t\t\treturn Promise.resolve();\n\t\t} else if (opts.soft) {\n\t\t\tconst rate = opts.soft === true ? 0.5 : +opts.soft;\n\t\t\tinv_mass_recovery_rate = 1 / (rate * 60);\n\t\t\tinv_mass = 0; // infinite mass, unaffected by spring forces\n\t\t}\n\t\tif (!task) {\n\t\t\tlast_time = now();\n\t\t\tcancel_task = false;\n\t\t\ttask = loop((now) => {\n\t\t\t\tif (cancel_task) {\n\t\t\t\t\tcancel_task = false;\n\t\t\t\t\ttask = null;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tinv_mass = Math.min(inv_mass + inv_mass_recovery_rate, 1);\n\t\t\t\tconst ctx = {\n\t\t\t\t\tinv_mass,\n\t\t\t\t\topts: spring,\n\t\t\t\t\tsettled: true,\n\t\t\t\t\tdt: ((now - last_time) * 60) / 1000\n\t\t\t\t};\n\t\t\t\tconst next_value = tick_spring(ctx, last_value, value, target_value);\n\t\t\t\tlast_time = now;\n\t\t\t\tlast_value = value;\n\t\t\t\tstore.set((value = next_value));\n\t\t\t\tif (ctx.settled) {\n\t\t\t\t\ttask = null;\n\t\t\t\t}\n\t\t\t\treturn !ctx.settled;\n\t\t\t});\n\t\t}\n\t\treturn new Promise((fulfil) => {\n\t\t\ttask.promise.then(() => {\n\t\t\t\tif (token === current_token) fulfil();\n\t\t\t});\n\t\t});\n\t}\n\t/** @type {import('./public.js').Spring<T>} */\n\tconst spring = {\n\t\tset,\n\t\tupdate: (fn, opts) => set(fn(target_value, value), opts),\n\t\tsubscribe: store.subscribe,\n\t\tstiffness,\n\t\tdamping,\n\t\tprecision\n\t};\n\treturn spring;\n}\n", "import { writable } from '../store/index.js';\nimport { assign, loop, now } from '../internal/index.js';\nimport { linear } from '../easing/index.js';\nimport { is_date } from './utils.js';\n\n/** @returns {(t: any) => any} */\nfunction get_interpolator(a, b) {\n\tif (a === b || a !== a) return () => a;\n\tconst type = typeof a;\n\tif (type !== typeof b || Array.isArray(a) !== Array.isArray(b)) {\n\t\tthrow new Error('Cannot interpolate values of different type');\n\t}\n\tif (Array.isArray(a)) {\n\t\tconst arr = b.map((bi, i) => {\n\t\t\treturn get_interpolator(a[i], bi);\n\t\t});\n\t\treturn (t) => arr.map((fn) => fn(t));\n\t}\n\tif (type === 'object') {\n\t\tif (!a || !b) throw new Error('Object cannot be null');\n\t\tif (is_date(a) && is_date(b)) {\n\t\t\ta = a.getTime();\n\t\t\tb = b.getTime();\n\t\t\tconst delta = b - a;\n\t\t\treturn (t) => new Date(a + t * delta);\n\t\t}\n\t\tconst keys = Object.keys(b);\n\t\tconst interpolators = {};\n\t\tkeys.forEach((key) => {\n\t\t\tinterpolators[key] = get_interpolator(a[key], b[key]);\n\t\t});\n\t\treturn (t) => {\n\t\t\tconst result = {};\n\t\t\tkeys.forEach((key) => {\n\t\t\t\tresult[key] = interpolators[key](t);\n\t\t\t});\n\t\t\treturn result;\n\t\t};\n\t}\n\tif (type === 'number') {\n\t\tconst delta = b - a;\n\t\treturn (t) => a + t * delta;\n\t}\n\tthrow new Error(`Cannot interpolate ${type} values`);\n}\n\n/**\n * A tweened store in Svelte is a special type of store that provides smooth transitions between state values over time.\n *\n * https://svelte.dev/docs/svelte-motion#tweened\n * @template T\n * @param {T} [value]\n * @param {import('./private.js').TweenedOptions<T>} [defaults]\n * @returns {import('./public.js').Tweened<T>}\n */\nexport function tweened(value, defaults = {}) {\n\tconst store = writable(value);\n\t/** @type {import('../internal/private.js').Task} */\n\tlet task;\n\tlet target_value = value;\n\t/**\n\t * @param {T} new_value\n\t * @param {import('./private.js').TweenedOptions<T>} [opts]\n\t */\n\tfunction set(new_value, opts) {\n\t\tif (value == null) {\n\t\t\tstore.set((value = new_value));\n\t\t\treturn Promise.resolve();\n\t\t}\n\t\ttarget_value = new_value;\n\t\tlet previous_task = task;\n\t\tlet started = false;\n\t\tlet {\n\t\t\tdelay = 0,\n\t\t\tduration = 400,\n\t\t\teasing = linear,\n\t\t\tinterpolate = get_interpolator\n\t\t} = assign(assign({}, defaults), opts);\n\t\tif (duration === 0) {\n\t\t\tif (previous_task) {\n\t\t\t\tprevious_task.abort();\n\t\t\t\tprevious_task = null;\n\t\t\t}\n\t\t\tstore.set((value = target_value));\n\t\t\treturn Promise.resolve();\n\t\t}\n\t\tconst start = now() + delay;\n\t\tlet fn;\n\t\ttask = loop((now) => {\n\t\t\tif (now < start) return true;\n\t\t\tif (!started) {\n\t\t\t\tfn = interpolate(value, new_value);\n\t\t\t\tif (typeof duration === 'function') duration = duration(value, new_value);\n\t\t\t\tstarted = true;\n\t\t\t}\n\t\t\tif (previous_task) {\n\t\t\t\tprevious_task.abort();\n\t\t\t\tprevious_task = null;\n\t\t\t}\n\t\t\tconst elapsed = now - start;\n\t\t\tif (elapsed > /** @type {number} */ (duration)) {\n\t\t\t\tstore.set((value = new_value));\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t// @ts-ignore\n\t\t\tstore.set((value = fn(easing(elapsed / duration))));\n\t\t\treturn true;\n\t\t});\n\t\treturn task.promise;\n\t}\n\treturn {\n\t\tset,\n\t\tupdate: (fn, opts) => set(fn(target_value, value), opts),\n\t\tsubscribe: store.subscribe\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;AAIO,SAAS,QAAQ,KAAK;AAC5B,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAChD;;;ACMA,SAAS,YAAY,KAAK,YAAY,eAAe,cAAc;AAClE,MAAI,OAAO,kBAAkB,YAAY,QAAQ,aAAa,GAAG;AAEhE,UAAM,QAAQ,eAAe;AAE7B,UAAM,YAAY,gBAAgB,eAAe,IAAI,MAAM,IAAI;AAC/D,UAAMA,UAAS,IAAI,KAAK,YAAY;AACpC,UAAM,SAAS,IAAI,KAAK,UAAU;AAClC,UAAM,gBAAgBA,UAAS,UAAU,IAAI;AAC7C,UAAM,KAAK,WAAW,gBAAgB,IAAI;AAC1C,QAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,WAAW;AAC7E,aAAO;AAAA,IACR,OAAO;AACN,UAAI,UAAU;AAEd,aAAO,QAAQ,aAAa,IAAI,IAAI,KAAK,cAAc,QAAQ,IAAI,CAAC,IAAI,gBAAgB;AAAA,IACzF;AAAA,EACD,WAAW,MAAM,QAAQ,aAAa,GAAG;AAExC,WAAO,cAAc;AAAA,MAAI,CAAC,GAAG,MAC5B,YAAY,KAAK,WAAW,CAAC,GAAG,cAAc,CAAC,GAAG,aAAa,CAAC,CAAC;AAAA,IAClE;AAAA,EACD,WAAW,OAAO,kBAAkB,UAAU;AAC7C,UAAM,aAAa,CAAC;AACpB,eAAW,KAAK,eAAe;AAE9B,iBAAW,CAAC,IAAI,YAAY,KAAK,WAAW,CAAC,GAAG,cAAc,CAAC,GAAG,aAAa,CAAC,CAAC;AAAA,IAClF;AAEA,WAAO;AAAA,EACR,OAAO;AACN,UAAM,IAAI,MAAM,iBAAiB,OAAO,aAAa,SAAS;AAAA,EAC/D;AACD;AAWO,SAAS,OAAO,OAAO,OAAO,CAAC,GAAG;AACxC,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,EAAE,YAAY,MAAM,UAAU,KAAK,YAAY,KAAK,IAAI;AAE9D,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI,aAAa;AAEjB,MAAI,eAAe;AACnB,MAAI,WAAW;AACf,MAAI,yBAAyB;AAC7B,MAAI,cAAc;AAMlB,WAAS,IAAI,WAAWC,QAAO,CAAC,GAAG;AAClC,mBAAe;AACf,UAAM,QAAS,gBAAgB,CAAC;AAChC,QAAI,SAAS,QAAQA,MAAK,QAASD,QAAO,aAAa,KAAKA,QAAO,WAAW,GAAI;AACjF,oBAAc;AACd,kBAAY,IAAI;AAChB,mBAAa;AACb,YAAM,IAAK,QAAQ,YAAa;AAChC,aAAO,QAAQ,QAAQ;AAAA,IACxB,WAAWC,MAAK,MAAM;AACrB,YAAM,OAAOA,MAAK,SAAS,OAAO,MAAM,CAACA,MAAK;AAC9C,+BAAyB,KAAK,OAAO;AACrC,iBAAW;AAAA,IACZ;AACA,QAAI,CAAC,MAAM;AACV,kBAAY,IAAI;AAChB,oBAAc;AACd,aAAO,KAAK,CAACC,SAAQ;AACpB,YAAI,aAAa;AAChB,wBAAc;AACd,iBAAO;AACP,iBAAO;AAAA,QACR;AACA,mBAAW,KAAK,IAAI,WAAW,wBAAwB,CAAC;AACxD,cAAM,MAAM;AAAA,UACX;AAAA,UACA,MAAMF;AAAA,UACN,SAAS;AAAA,UACT,KAAME,OAAM,aAAa,KAAM;AAAA,QAChC;AACA,cAAM,aAAa,YAAY,KAAK,YAAY,OAAO,YAAY;AACnE,oBAAYA;AACZ,qBAAa;AACb,cAAM,IAAK,QAAQ,UAAW;AAC9B,YAAI,IAAI,SAAS;AAChB,iBAAO;AAAA,QACR;AACA,eAAO,CAAC,IAAI;AAAA,MACb,CAAC;AAAA,IACF;AACA,WAAO,IAAI,QAAQ,CAAC,WAAW;AAC9B,WAAK,QAAQ,KAAK,MAAM;AACvB,YAAI,UAAU,cAAe,QAAO;AAAA,MACrC,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAEA,QAAMF,UAAS;AAAA,IACd;AAAA,IACA,QAAQ,CAAC,IAAIC,UAAS,IAAI,GAAG,cAAc,KAAK,GAAGA,KAAI;AAAA,IACvD,WAAW,MAAM;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,SAAOD;AACR;;;AC/HA,SAAS,iBAAiB,GAAG,GAAG;AAC/B,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO,MAAM;AACrC,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,OAAO,KAAK,MAAM,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,GAAG;AAC/D,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AACA,MAAI,MAAM,QAAQ,CAAC,GAAG;AACrB,UAAM,MAAM,EAAE,IAAI,CAAC,IAAI,MAAM;AAC5B,aAAO,iBAAiB,EAAE,CAAC,GAAG,EAAE;AAAA,IACjC,CAAC;AACD,WAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,EACpC;AACA,MAAI,SAAS,UAAU;AACtB,QAAI,CAAC,KAAK,CAAC,EAAG,OAAM,IAAI,MAAM,uBAAuB;AACrD,QAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,GAAG;AAC7B,UAAI,EAAE,QAAQ;AACd,UAAI,EAAE,QAAQ;AACd,YAAM,QAAQ,IAAI;AAClB,aAAO,CAAC,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IACrC;AACA,UAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,UAAM,gBAAgB,CAAC;AACvB,SAAK,QAAQ,CAAC,QAAQ;AACrB,oBAAc,GAAG,IAAI,iBAAiB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,IACrD,CAAC;AACD,WAAO,CAAC,MAAM;AACb,YAAM,SAAS,CAAC;AAChB,WAAK,QAAQ,CAAC,QAAQ;AACrB,eAAO,GAAG,IAAI,cAAc,GAAG,EAAE,CAAC;AAAA,MACnC,CAAC;AACD,aAAO;AAAA,IACR;AAAA,EACD;AACA,MAAI,SAAS,UAAU;AACtB,UAAM,QAAQ,IAAI;AAClB,WAAO,CAAC,MAAM,IAAI,IAAI;AAAA,EACvB;AACA,QAAM,IAAI,MAAM,sBAAsB,IAAI,SAAS;AACpD;AAWO,SAAS,QAAQ,OAAO,WAAW,CAAC,GAAG;AAC7C,QAAM,QAAQ,SAAS,KAAK;AAE5B,MAAI;AACJ,MAAI,eAAe;AAKnB,WAAS,IAAI,WAAW,MAAM;AAC7B,QAAI,SAAS,MAAM;AAClB,YAAM,IAAK,QAAQ,SAAU;AAC7B,aAAO,QAAQ,QAAQ;AAAA,IACxB;AACA,mBAAe;AACf,QAAI,gBAAgB;AACpB,QAAI,UAAU;AACd,QAAI;AAAA,MACH,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,cAAc;AAAA,IACf,IAAI,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG,IAAI;AACrC,QAAI,aAAa,GAAG;AACnB,UAAI,eAAe;AAClB,sBAAc,MAAM;AACpB,wBAAgB;AAAA,MACjB;AACA,YAAM,IAAK,QAAQ,YAAa;AAChC,aAAO,QAAQ,QAAQ;AAAA,IACxB;AACA,UAAM,QAAQ,IAAI,IAAI;AACtB,QAAI;AACJ,WAAO,KAAK,CAACG,SAAQ;AACpB,UAAIA,OAAM,MAAO,QAAO;AACxB,UAAI,CAAC,SAAS;AACb,aAAK,YAAY,OAAO,SAAS;AACjC,YAAI,OAAO,aAAa,WAAY,YAAW,SAAS,OAAO,SAAS;AACxE,kBAAU;AAAA,MACX;AACA,UAAI,eAAe;AAClB,sBAAc,MAAM;AACpB,wBAAgB;AAAA,MACjB;AACA,YAAM,UAAUA,OAAM;AACtB,UAAI;AAAA,MAAiC,UAAW;AAC/C,cAAM,IAAK,QAAQ,SAAU;AAC7B,eAAO;AAAA,MACR;AAEA,YAAM,IAAK,QAAQ,GAAG,OAAO,UAAU,QAAQ,CAAC,CAAE;AAClD,aAAO;AAAA,IACR,CAAC;AACD,WAAO,KAAK;AAAA,EACb;AACA,SAAO;AAAA,IACN;AAAA,IACA,QAAQ,CAAC,IAAI,SAAS,IAAI,GAAG,cAAc,KAAK,GAAG,IAAI;AAAA,IACvD,WAAW,MAAM;AAAA,EAClB;AACD;", "names": ["spring", "opts", "now", "now"]}