<execution>
  <constraint>
    ## ASCII绘图技术限制
    - **字符集约束**：只能使用标准ASCII字符集
    - **对齐精度**：受字符宽度和终端设置影响
    - **复杂度限制**：过于复杂的图形难以用ASCII表达
    - **兼容性要求**：必须在不同平台保持一致显示效果
  </constraint>

  <rule>
    ## ASCII绘图强制规则
    - **字符标准化**：只使用标准ASCII字符，避免特殊符号
    - **对齐严格性**：所有线条和边框必须严格对齐
    - **比例一致性**：同类元素必须保持相同的比例关系
    - **可读性优先**：图形清晰度优于艺术美观性
  </rule>

  <guideline>
    ## ASCII绘图指导原则
    - **简洁有效**：用最少的字符表达最清晰的信息
    - **功能导向**：图形服务于信息传达而非装饰
    - **标准化复用**：建立标准图形模板便于复用
    - **渐进优化**：从基础图形开始逐步完善细节
  </guideline>

  <process>
    ## ASCII绘图标准流程
    
    ### Step 1: 图形需求分析 (20%)
    
    ```
    需求输入 ──▶ 图形类型选择 ──▶ 复杂度评估 ──▶ 可行性确认
    ```
    
    **图形类型库**：
    ```
    ┌─ 流程图 ────┐  ┌─ 架构图 ────┐  ┌─ 表格图 ────┐
    │ • 决策流程  │  │ • 系统结构  │  │ • 数据表格  │
    │ • 操作步骤  │  │ • 组件关系  │  │ • 对比矩阵  │
    │ • 状态转换  │  │ • 层次结构  │  │ • 统计图表  │
    └─────────────┘  └─────────────┘  └─────────────┘
    ```
    
    ### Step 2: 基础框架构建 (30%)
    
    ```
    ┌─────────────────────────────────────────────────────────┐
    │                    框架设计模板                          │
    ├─────────────────────────────────────────────────────────┤
    │                                                         │
    │  ┌─────────┐    ┌─────────┐    ┌─────────┐              │
    │  │  模块A  │───▶│  模块B  │───▶│  模块C  │              │
    │  └─────────┘    └─────────┘    └─────────┘              │
    │                                                         │
    └─────────────────────────────────────────────────────────┘
    ```
    
    **基础字符集**：
    ```
    边框字符：┌ ┐ └ ┘ ├ ┤ ┬ ┴ ┼ ─ │
    连接字符：─ │ ┌ ┐ └ ┘ ├ ┤ ┬ ┴ ┼
    箭头字符：→ ← ↑ ↓ ▶ ◀ ▲ ▼
    装饰字符：• ○ ● ◆ ◇ ■ □ ▪ ▫
    ```
    
    ### Step 3: 细节填充与优化 (35%)
    
    ```
    基础框架 ──▶ 添加内容 ──▶ 调整对齐 ──▶ 优化视觉效果
    ```
    
    **复杂图形示例**：
    ```
    ┌─────────────────────────────────────────────────────────────┐
    │                      系统架构图                              │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
    │  │   用户层    │    │   应用层    │    │   数据层    │      │
    │  │             │    │             │    │             │      │
    │  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │      │
    │  │ │ Web界面 │ │───▶│ │业务逻辑 │ │───▶│ │ 数据库  │ │      │
    │  │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │      │
    │  │             │    │             │    │             │      │
    │  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │      │
    │  │ │移动应用 │ │───▶│ │API网关 │ │───▶│ │文件系统 │ │      │
    │  │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │      │
    │  └─────────────┘    └─────────────┘    └─────────────┘      │
    │                                                             │
    └─────────────────────────────────────────────────────────────┘
    ```
    
    ### Step 4: 质量检查与最终调整 (15%)
    
    ```
    对齐检查 ──▶ 比例验证 ──▶ 兼容性测试 ──▶ 最终确认
    ```
    
    **质量检查清单**：
    ```
    ┌─ 对齐检查 ──────────────────────────────────────────┐
    │ □ 垂直线条是否严格对齐                              │
    │ □ 水平线条是否保持水平                              │
    │ □ 边框是否完整闭合                                  │
    │ □ 连接点是否准确连接                                │
    └─────────────────────────────────────────────────────┘
    
    ┌─ 视觉效果 ──────────────────────────────────────────┐
    │ □ 整体布局是否平衡                                  │
    │ □ 信息层次是否清晰                                  │
    │ □ 重点内容是否突出                                  │
    │ □ 图形是否易于理解                                  │
    └─────────────────────────────────────────────────────┘
    ```
  </process>

  <criteria>
    ## ASCII绘图质量标准
    
    ### 技术质量指标
    - ✅ 字符对齐精度 ≥ 99%
    - ✅ 线条连接准确率 ≥ 98%
    - ✅ 比例协调性 ≥ 90%
    - ✅ 兼容性表现 ≥ 95%
    
    ### 视觉质量指标
    - ✅ 整体美观度 ≥ 85%
    - ✅ 信息清晰度 ≥ 90%
    - ✅ 层次分明度 ≥ 85%
    - ✅ 重点突出度 ≥ 80%
    
    ### 功能质量指标
    - ✅ 信息传达准确率 ≥ 95%
    - ✅ 理解便利性 ≥ 85%
    - ✅ 使用实用性 ≥ 80%
    - ✅ 复用可行性 ≥ 75%
    
    ### 效率质量指标
    - ✅ 绘制效率 ≥ 80%
    - ✅ 修改便利性 ≥ 75%
    - ✅ 维护成本控制 ≥ 80%
    - ✅ 标准化程度 ≥ 85%
  </criteria>
</execution>
