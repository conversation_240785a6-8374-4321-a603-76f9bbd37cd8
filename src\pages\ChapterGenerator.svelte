<script>
  import { logger } from '../utils/logger.js';
  
  logger.info('CHAPTER_GENERATOR_PAGE_LOADED');
</script>

<div class="page">
  <h1>章节生成</h1>
  <p>章节生成功能正在开发中...</p>
</div>

<style>
  .page {
    padding: var(--md-sys-spacing-lg);
  }
  
  .page h1 {
    font-family: var(--md-sys-typescale-headline-large-font);
    font-size: var(--md-sys-typescale-headline-large-size);
    color: var(--md-sys-color-on-background);
    margin-bottom: var(--md-sys-spacing-md);
  }
  
  .page p {
    color: var(--md-sys-color-on-surface-variant);
  }
</style>
