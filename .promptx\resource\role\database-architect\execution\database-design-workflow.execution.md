<execution>
  <constraint>
    ## 技术约束
    - **IndexedDB版本**：使用IndexedDB 2.0 API，确保现代浏览器兼容
    - **数据库名称**：AINovelAssistant，版本号从1开始递增
    - **存储限制**：单个数据库<1GB，单条记录<100MB
    - **浏览器支持**：Chrome 58+, Firefox 52+, Safari 10.1+
    - **性能要求**：查询响应<100ms，批量操作支持1000条/秒

    ## 业务约束
    - **数据表数量**：必须支持25个核心数据表
    - **离线支持**：完全离线数据访问，无网络依赖
    - **数据安全**：敏感数据加密存储，API密钥保护
    - **版本兼容**：支持数据模型升级和向后兼容
    - **备份恢复**：提供完整的数据备份和恢复机制
  </constraint>

  <rule>
    ## 设计规则
    - **命名规范**：表名使用camelCase，字段名保持一致性
    - **主键设计**：所有表必须有唯一主键，优先使用UUID
    - **索引策略**：为常用查询字段创建索引，避免过度索引
    - **事务边界**：相关操作必须在同一事务中完成
    - **数据类型**：使用JavaScript原生类型，避免复杂对象嵌套

    ## 开发规则
    - **Repository模式**：所有数据访问必须通过Repository类
    - **异步操作**：所有数据库操作必须使用async/await
    - **错误处理**：完善的异常捕获和错误恢复机制
    - **类型安全**：使用TypeScript定义数据模型接口
    - **测试覆盖**：核心数据操作必须有单元测试
  </rule>

  <guideline>
    ## 设计指导原则
    - **性能优先**：优化查询性能，合理使用索引和缓存
    - **数据完整性**：通过事务和约束保证数据一致性
    - **可扩展性**：设计灵活的数据模型支持功能扩展
    - **用户体验**：确保数据操作的响应速度和可靠性
    - **维护性**：清晰的代码结构和完善的文档

    ## IndexedDB最佳实践
    - **事务管理**：合理控制事务范围，避免长事务
    - **批量操作**：使用事务进行批量插入和更新
    - **索引优化**：根据查询模式设计合适的索引
    - **错误恢复**：实现数据损坏时的自动恢复机制
    - **版本升级**：平滑的数据模型升级策略
  </guideline>

  <process>
    ## 数据架构开发流程
    
    ### 1. 数据模型设计
    ```mermaid
    flowchart TD
        A[需求分析] --> B[实体识别]
        B --> C[属性定义]
        C --> D[关系设计]
        D --> E[索引规划]
        E --> F[约束定义]
        F --> G[模型验证]
        G --> H[文档编写]
    ```

    ### 2. 数据库实现
    ```mermaid
    flowchart TD
        A[数据库架构] --> B[表结构创建]
        B --> C[索引创建]
        C --> D[Repository实现]
        D --> E[事务管理]
        E --> F[错误处理]
        F --> G[单元测试]
        G --> H[集成测试]
    ```

    ### 3. 性能优化
    ```mermaid
    flowchart TD
        A[性能分析] --> B[查询优化]
        B --> C[索引调优]
        C --> D[缓存策略]
        D --> E[批量操作]
        E --> F[内存管理]
        F --> G[性能测试]
        G --> H[监控告警]
    ```

    ## 质量保证流程
    
    ### 数据完整性测试
    - **CRUD测试**：基本的增删改查操作测试
    - **事务测试**：事务提交和回滚机制测试
    - **并发测试**：多用户并发访问测试
    - **约束测试**：数据约束和关系完整性测试
    - **恢复测试**：数据损坏和恢复机制测试

    ### 发布前检查清单
    - [ ] 25个数据表全部创建完成
    - [ ] 所有索引设计合理有效
    - [ ] Repository类功能完整
    - [ ] 事务管理机制正常
    - [ ] 错误处理覆盖所有场景
    - [ ] 数据迁移机制工作正常
    - [ ] 性能指标满足要求
    - [ ] 单元测试覆盖率>80%
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 功能完整性
    - ✅ 25个核心数据表全部实现
    - ✅ Repository模式完整封装
    - ✅ 事务管理机制完善
    - ✅ 数据迁移功能正常
    - ✅ 备份恢复机制可用

    ### 性能指标
    - ✅ 查询响应时间 < 100ms
    - ✅ 批量操作性能 > 1000条/秒
    - ✅ 数据库大小 < 1GB
    - ✅ 内存使用 < 100MB
    - ✅ 索引命中率 > 90%

    ### 数据完整性
    - ✅ 事务ACID特性保证
    - ✅ 数据约束有效执行
    - ✅ 关系完整性维护
    - ✅ 并发控制机制正常
    - ✅ 错误恢复机制可靠

    ### 可维护性
    - ✅ 代码结构清晰模块化
    - ✅ 接口设计统一规范
    - ✅ 错误处理完善健壮
    - ✅ 文档详细准确
    - ✅ 测试覆盖率充分

    ### 用户体验
    - ✅ 数据操作响应迅速
    - ✅ 离线功能完全可用
    - ✅ 数据安全可靠
    - ✅ 错误提示友好
    - ✅ 数据恢复简单有效
  </criteria>
</execution>
