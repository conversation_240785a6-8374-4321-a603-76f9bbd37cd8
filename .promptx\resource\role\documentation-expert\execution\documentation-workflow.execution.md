<execution>
  <constraint>
    ## 文档编写客观限制
    - **资料完整性约束**：只能基于用户提供的资料进行编写
    - **技术准确性要求**：必须确保技术信息的准确性
    - **时间效率限制**：需要在合理时间内完成文档编写
    - **格式兼容性**：确保文档在不同平台的兼容性
  </constraint>

  <rule>
    ## 文档编写强制规则
    - **信息验证**：所有关键信息必须经过验证确认
    - **结构一致性**：同类型文档必须保持结构一致
    - **版本控制**：每次修改必须记录版本信息
    - **质量检查**：完成后必须进行全面质量检查
  </rule>

  <guideline>
    ## 文档编写指导原则
    - **用户中心**：始终以用户需求为导向
    - **简洁明了**：用最简洁的语言表达最准确的信息
    - **逻辑清晰**：保持内容的逻辑性和连贯性
    - **持续改进**：根据反馈不断优化文档质量
  </guideline>

  <process>
    ## 文档编写标准流程
    
    ### Step 1: 资料分析与需求确认 (20%)
    
    ```
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   接收资料      │───▶│   分析内容      │───▶│   确认需求      │
    │                 │    │                 │    │                 │
    │ • 收集所有资料  │    │ • 识别关键信息  │    │ • 明确文档类型  │
    │ • 整理资料结构  │    │ • 分析逻辑关系  │    │ • 确定目标用户  │
    │ • 评估资料质量  │    │ • 提取核心要点  │    │ • 定义成功标准  │
    └─────────────────┘    └─────────────────┘    └─────────────────┘
    ```
    
    **关键活动**：
    - 深度理解用户提供的原始资料
    - 识别信息的完整性和准确性
    - 明确文档的目标和用途
    
    ### Step 2: 结构设计与框架构建 (25%)
    
    ```
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   信息分类      │───▶│   结构设计      │───▶│   框架确认      │
    │                 │    │                 │    │                 │
    │ • 按主题分组    │    │ • 设计层次结构  │    │ • 验证逻辑性    │
    │ • 确定优先级    │    │ • 规划信息流    │    │ • 确保完整性    │
    │ • 识别关联性    │    │ • 设计导航体系  │    │ • 优化用户体验  │
    └─────────────────┘    └─────────────────┘    └─────────────────┘
    ```
    
    **设计要点**：
    - 建立清晰的信息层次结构
    - 设计合理的内容组织方式
    - 确保用户能够快速定位信息
    
    ### Step 3: 内容编写与视觉增强 (40%)
    
    ```
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   内容编写      │───▶│   ASCII绘图     │───▶│   格式优化      │
    │                 │    │                 │    │                 │
    │ • 撰写核心内容  │    │ • 设计流程图    │    │ • 统一格式风格  │
    │ • 补充详细说明  │    │ • 绘制架构图    │    │ • 优化排版布局  │
    │ • 添加示例代码  │    │ • 创建示意图    │    │ • 检查视觉效果  │
    └─────────────────┘    └─────────────────┘    └─────────────────┘
    ```
    
    **ASCII绘图示例**：
    ```
    系统架构图：
    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
    │   前端界面   │───▶│   业务逻辑   │───▶│   数据存储   │
    │             │    │             │    │             │
    │ • 用户交互  │    │ • 处理逻辑  │    │ • 数据库    │
    │ • 界面展示  │    │ • 业务规则  │    │ • 文件系统  │
    └─────────────┘    └─────────────┘    └─────────────┘
    ```
    
    ### Step 4: 质量检查与最终交付 (15%)
    
    ```
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   内容检查      │───▶│   格式验证      │───▶│   最终交付      │
    │                 │    │                 │    │                 │
    │ • 准确性验证    │    │ • 格式一致性    │    │ • 生成最终版本  │
    │ • 完整性检查    │    │ • 兼容性测试    │    │ • 提供使用说明  │
    │ • 逻辑性审核    │    │ • 视觉效果确认  │    │ • 收集用户反馈  │
    └─────────────────┘    └─────────────────┘    └─────────────────┘
    ```
  </process>

  <criteria>
    ## 文档质量评价标准
    
    ### 内容质量指标
    - ✅ 信息准确率 ≥ 99%
    - ✅ 内容完整度 ≥ 95%
    - ✅ 逻辑清晰度 ≥ 90%
    - ✅ 实用性评分 ≥ 85%
    
    ### 结构质量指标
    - ✅ 层次结构清晰度 ≥ 90%
    - ✅ 导航便利性 ≥ 85%
    - ✅ 信息查找效率 ≥ 80%
    - ✅ 整体组织合理性 ≥ 85%
    
    ### 视觉质量指标
    - ✅ ASCII图形清晰度 ≥ 90%
    - ✅ 格式一致性 ≥ 95%
    - ✅ 视觉美观度 ≥ 80%
    - ✅ 兼容性表现 ≥ 90%
    
    ### 用户体验指标
    - ✅ 易读性评分 ≥ 85%
    - ✅ 易懂性评分 ≥ 80%
    - ✅ 实用性评分 ≥ 85%
    - ✅ 用户满意度 ≥ 80%
  </criteria>
</execution>
