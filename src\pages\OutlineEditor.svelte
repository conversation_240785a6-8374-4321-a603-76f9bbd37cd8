<script>
  import { logger } from '../utils/logger.js';
  
  logger.info('OUTLINE_EDITOR_PAGE_LOADED');
</script>

<div class="page">
  <h1>大纲编辑</h1>
  <p>大纲编辑功能正在开发中...</p>
</div>

<style>
  .page {
    padding: var(--md-sys-spacing-lg);
  }
  
  .page h1 {
    font-family: var(--md-sys-typescale-headline-large-font);
    font-size: var(--md-sys-typescale-headline-large-size);
    color: var(--md-sys-color-on-background);
    margin-bottom: var(--md-sys-spacing-md);
  }
  
  .page p {
    color: var(--md-sys-color-on-surface-variant);
  }
</style>
