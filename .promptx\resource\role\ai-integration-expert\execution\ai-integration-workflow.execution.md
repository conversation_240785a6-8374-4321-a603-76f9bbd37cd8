<execution>
  <constraint>
    ## 技术约束
    - **AI服务支持**：必须支持OpenAI、<PERSON>、Gemini、自定义、ModelScope、Ollama、SiliconFlow共7种服务
    - **浏览器兼容**：支持现代浏览器的Fetch API和WebSocket
    - **安全要求**：API密钥必须加密存储，不能明文传输
    - **性能要求**：API响应时间<5秒，支持流式响应
    - **离线支持**：支持Ollama等本地模型，提供离线AI能力

    ## 业务约束
    - **智能检测**：API地址输入错误时必须自动检测和纠正
    - **动态模型**：必须支持自动获取最新模型列表
    - **降AI味**：必须内置AI内容优化算法
    - **统一管理**：所有AI服务使用统一的配置界面
    - **错误处理**：提供用户友好的错误提示和解决建议
  </constraint>

  <rule>
    ## 开发规则
    - **统一接口**：所有AI服务必须实现BaseAPIService接口
    - **错误分类**：按网络、认证、业务、限流等维度分类处理错误
    - **重试机制**：实现指数退避重试，最大重试3次
    - **并发控制**：同一服务最大并发请求数不超过5个
    - **缓存策略**：相同请求24小时内返回缓存结果

    ## 安全规则
    - **密钥加密**：使用AES-256加密存储API密钥
    - **传输安全**：所有API调用必须使用HTTPS
    - **权限验证**：每次调用前验证API密钥有效性
    - **数据脱敏**：日志中不能包含敏感信息
    - **访问控制**：实现API调用频率限制
  </rule>

  <guideline>
    ## 集成指导原则
    - **渐进增强**：优先实现核心功能，逐步增加高级特性
    - **用户体验**：提供实时进度反馈和取消操作功能
    - **容错设计**：单个服务故障不影响其他服务使用
    - **性能优先**：优化API调用效率，减少用户等待时间
    - **可扩展性**：新增AI服务只需实现适配器接口

    ## API管理指南
    - **地址检测**：支持常见的API地址格式和错误纠正
    - **模型发现**：定期更新模型列表，支持手动刷新
    - **健康检查**：定期检测API服务状态和响应时间
    - **智能路由**：根据模型能力和成本智能选择服务
    - **降级策略**：主服务不可用时自动切换备用服务
  </guideline>

  <process>
    ## AI集成开发流程
    
    ### 1. 服务适配器开发
    ```mermaid
    flowchart TD
        A[分析API文档] --> B[设计适配器接口]
        B --> C[实现认证机制]
        C --> D[实现请求转换]
        D --> E[实现响应解析]
        E --> F[错误处理实现]
        F --> G[单元测试]
        G --> H[集成测试]
    ```

    ### 2. 智能检测实现
    ```mermaid
    flowchart TD
        A[收集常见错误] --> B[设计纠错规则]
        B --> C[实现检测算法]
        C --> D[实现纠正逻辑]
        D --> E[连通性测试]
        E --> F[模型发现]
        F --> G[缓存机制]
        G --> H[验证测试]
    ```

    ### 3. 降AI味算法
    ```mermaid
    flowchart TD
        A[分析AI生成特征] --> B[设计检测规则]
        B --> C[实现优化算法]
        C --> D[人性化改写]
        D --> E[风格一致性]
        E --> F[效果评估]
        F --> G[参数调优]
        G --> H[性能测试]
    ```

    ## 质量保证流程
    
    ### API集成测试
    - **单元测试**：每个适配器的独立功能测试
    - **集成测试**：多服务协同工作测试
    - **性能测试**：并发调用和响应时间测试
    - **错误测试**：各种异常情况的处理测试
    - **安全测试**：密钥管理和数据传输安全测试

    ### 发布前检查清单
    - [ ] 所有7种AI服务集成完成
    - [ ] 智能API检测功能正常
    - [ ] 动态模型发现工作正常
    - [ ] 降AI味算法效果良好
    - [ ] 错误处理覆盖所有场景
    - [ ] API密钥加密存储安全
    - [ ] 性能指标满足要求
    - [ ] 用户体验流畅友好
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 功能完整性
    - ✅ 支持7种AI服务完整集成
    - ✅ 智能API地址检测和纠正
    - ✅ 动态模型列表获取和缓存
    - ✅ 降AI味算法效果显著
    - ✅ 统一配置管理界面

    ### 性能指标
    - ✅ API响应时间 < 5秒
    - ✅ 并发请求处理能力 > 5个
    - ✅ 错误重试成功率 > 90%
    - ✅ 缓存命中率 > 70%
    - ✅ 内存使用 < 50MB

    ### 安全性
    - ✅ API密钥AES-256加密存储
    - ✅ 所有请求使用HTTPS传输
    - ✅ 敏感信息不出现在日志中
    - ✅ 访问频率限制有效
    - ✅ 权限验证机制完善

    ### 用户体验
    - ✅ 错误提示友好准确
    - ✅ 进度反馈实时清晰
    - ✅ 操作响应及时流畅
    - ✅ 配置界面简洁易用
    - ✅ 帮助文档完整详细

    ### 可维护性
    - ✅ 代码结构清晰模块化
    - ✅ 接口设计统一规范
    - ✅ 错误处理完善健壮
    - ✅ 日志记录详细有用
    - ✅ 文档更新及时准确
  </criteria>
</execution>
