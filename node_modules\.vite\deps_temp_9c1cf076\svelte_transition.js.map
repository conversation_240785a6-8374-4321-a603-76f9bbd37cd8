{"version": 3, "sources": ["../../svelte/src/runtime/transition/index.js"], "sourcesContent": ["import { cubicOut, cubicInOut, linear } from '../easing/index.js';\nimport { assign, split_css_unit, is_function } from '../internal/index.js';\n\n/**\n * Animates a `blur` filter alongside an element's opacity.\n *\n * https://svelte.dev/docs/svelte-transition#blur\n * @param {Element} node\n * @param {import('./public').BlurParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function blur(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicInOut, amount = 5, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst f = style.filter === 'none' ? '' : style.filter;\n\tconst od = target_opacity * (1 - opacity);\n\tconst [value, unit] = split_css_unit(amount);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_t, u) => `opacity: ${target_opacity - od * u}; filter: ${f} blur(${u * value}${unit});`\n\t};\n}\n\n/**\n * Animates the opacity of an element from 0 to the current opacity for `in` transitions and from the current opacity to 0 for `out` transitions.\n *\n * https://svelte.dev/docs/svelte-transition#fade\n * @param {Element} node\n * @param {import('./public').FadeParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function fade(node, { delay = 0, duration = 400, easing = linear } = {}) {\n\tconst o = +getComputedStyle(node).opacity;\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t) => `opacity: ${t * o}`\n\t};\n}\n\n/**\n * Animates the x and y positions and the opacity of an element. `in` transitions animate from the provided values, passed as parameters to the element's default values. `out` transitions animate from the element's default values to the provided values.\n *\n * https://svelte.dev/docs/svelte-transition#fly\n * @param {Element} node\n * @param {import('./public').FlyParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function fly(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicOut, x = 0, y = 0, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst od = target_opacity * (1 - opacity);\n\tconst [xValue, xUnit] = split_css_unit(x);\n\tconst [yValue, yUnit] = split_css_unit(y);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t, u) => `\n\t\t\ttransform: ${transform} translate(${(1 - t) * xValue}${xUnit}, ${(1 - t) * yValue}${yUnit});\n\t\t\topacity: ${target_opacity - od * u}`\n\t};\n}\n\n/**\n * Slides an element in and out.\n *\n * https://svelte.dev/docs/svelte-transition#slide\n * @param {Element} node\n * @param {import('./public').SlideParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function slide(node, { delay = 0, duration = 400, easing = cubicOut, axis = 'y' } = {}) {\n\tconst style = getComputedStyle(node);\n\tconst opacity = +style.opacity;\n\tconst primary_property = axis === 'y' ? 'height' : 'width';\n\tconst primary_property_value = parseFloat(style[primary_property]);\n\tconst secondary_properties = axis === 'y' ? ['top', 'bottom'] : ['left', 'right'];\n\tconst capitalized_secondary_properties = secondary_properties.map(\n\t\t(e) => `${e[0].toUpperCase()}${e.slice(1)}`\n\t);\n\tconst padding_start_value = parseFloat(style[`padding${capitalized_secondary_properties[0]}`]);\n\tconst padding_end_value = parseFloat(style[`padding${capitalized_secondary_properties[1]}`]);\n\tconst margin_start_value = parseFloat(style[`margin${capitalized_secondary_properties[0]}`]);\n\tconst margin_end_value = parseFloat(style[`margin${capitalized_secondary_properties[1]}`]);\n\tconst border_width_start_value = parseFloat(\n\t\tstyle[`border${capitalized_secondary_properties[0]}Width`]\n\t);\n\tconst border_width_end_value = parseFloat(\n\t\tstyle[`border${capitalized_secondary_properties[1]}Width`]\n\t);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t) =>\n\t\t\t'overflow: hidden;' +\n\t\t\t`opacity: ${Math.min(t * 20, 1) * opacity};` +\n\t\t\t`${primary_property}: ${t * primary_property_value}px;` +\n\t\t\t`padding-${secondary_properties[0]}: ${t * padding_start_value}px;` +\n\t\t\t`padding-${secondary_properties[1]}: ${t * padding_end_value}px;` +\n\t\t\t`margin-${secondary_properties[0]}: ${t * margin_start_value}px;` +\n\t\t\t`margin-${secondary_properties[1]}: ${t * margin_end_value}px;` +\n\t\t\t`border-${secondary_properties[0]}-width: ${t * border_width_start_value}px;` +\n\t\t\t`border-${secondary_properties[1]}-width: ${t * border_width_end_value}px;`\n\t};\n}\n\n/**\n * Animates the opacity and scale of an element. `in` transitions animate from an element's current (default) values to the provided values, passed as parameters. `out` transitions animate from the provided values to an element's default values.\n *\n * https://svelte.dev/docs/svelte-transition#scale\n * @param {Element} node\n * @param {import('./public').ScaleParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function scale(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicOut, start = 0, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst sd = 1 - start;\n\tconst od = target_opacity * (1 - opacity);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_t, u) => `\n\t\t\ttransform: ${transform} scale(${1 - sd * u});\n\t\t\topacity: ${target_opacity - od * u}\n\t\t`\n\t};\n}\n\n/**\n * Animates the stroke of an SVG element, like a snake in a tube. `in` transitions begin with the path invisible and draw the path to the screen over time. `out` transitions start in a visible state and gradually erase the path. `draw` only works with elements that have a `getTotalLength` method, like `<path>` and `<polyline>`.\n *\n * https://svelte.dev/docs/svelte-transition#draw\n * @param {SVGElement & { getTotalLength(): number }} node\n * @param {import('./public').DrawParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function draw(node, { delay = 0, speed, duration, easing = cubicInOut } = {}) {\n\tlet len = node.getTotalLength();\n\tconst style = getComputedStyle(node);\n\tif (style.strokeLinecap !== 'butt') {\n\t\tlen += parseInt(style.strokeWidth);\n\t}\n\tif (duration === undefined) {\n\t\tif (speed === undefined) {\n\t\t\tduration = 800;\n\t\t} else {\n\t\t\tduration = len / speed;\n\t\t}\n\t} else if (typeof duration === 'function') {\n\t\tduration = duration(len);\n\t}\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_, u) => `\n\t\t\tstroke-dasharray: ${len};\n\t\t\tstroke-dashoffset: ${u * len};\n\t\t`\n\t};\n}\n\n/**\n * The `crossfade` function creates a pair of [transitions](https://svelte.dev/docs#template-syntax-element-directives-transition-fn) called `send` and `receive`. When an element is 'sent', it looks for a corresponding element being 'received', and generates a transition that transforms the element to its counterpart's position and fades it out. When an element is 'received', the reverse happens. If there is no counterpart, the `fallback` transition is used.\n *\n * https://svelte.dev/docs/svelte-transition#crossfade\n * @param {import('./public').CrossfadeParams & {\n * \tfallback?: (node: Element, params: import('./public').CrossfadeParams, intro: boolean) => import('./public').TransitionConfig;\n * }} params\n * @returns {[(node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig, (node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig]}\n */\nexport function crossfade({ fallback, ...defaults }) {\n\t/** @type {Map<any, Element>} */\n\tconst to_receive = new Map();\n\t/** @type {Map<any, Element>} */\n\tconst to_send = new Map();\n\t/**\n\t * @param {Element} from_node\n\t * @param {Element} node\n\t * @param {import('./public').CrossfadeParams} params\n\t * @returns {import('./public').TransitionConfig}\n\t */\n\tfunction crossfade(from_node, node, params) {\n\t\tconst {\n\t\t\tdelay = 0,\n\t\t\tduration = (d) => Math.sqrt(d) * 30,\n\t\t\teasing = cubicOut\n\t\t} = assign(assign({}, defaults), params);\n\t\tconst from = from_node.getBoundingClientRect();\n\t\tconst to = node.getBoundingClientRect();\n\t\tconst dx = from.left - to.left;\n\t\tconst dy = from.top - to.top;\n\t\tconst dw = from.width / to.width;\n\t\tconst dh = from.height / to.height;\n\t\tconst d = Math.sqrt(dx * dx + dy * dy);\n\t\tconst style = getComputedStyle(node);\n\t\tconst transform = style.transform === 'none' ? '' : style.transform;\n\t\tconst opacity = +style.opacity;\n\t\treturn {\n\t\t\tdelay,\n\t\t\tduration: is_function(duration) ? duration(d) : duration,\n\t\t\teasing,\n\t\t\tcss: (t, u) => `\n\t\t\t\topacity: ${t * opacity};\n\t\t\t\ttransform-origin: top left;\n\t\t\t\ttransform: ${transform} translate(${u * dx}px,${u * dy}px) scale(${t + (1 - t) * dw}, ${\n\t\t\t\tt + (1 - t) * dh\n\t\t\t});\n\t\t\t`\n\t\t};\n\t}\n\n\t/**\n\t * @param {Map<any, Element>} items\n\t * @param {Map<any, Element>} counterparts\n\t * @param {boolean} intro\n\t * @returns {(node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig}\n\t */\n\tfunction transition(items, counterparts, intro) {\n\t\treturn (node, params) => {\n\t\t\titems.set(params.key, node);\n\t\t\treturn () => {\n\t\t\t\tif (counterparts.has(params.key)) {\n\t\t\t\t\tconst other_node = counterparts.get(params.key);\n\t\t\t\t\tcounterparts.delete(params.key);\n\t\t\t\t\treturn crossfade(other_node, node, params);\n\t\t\t\t}\n\t\t\t\t// if the node is disappearing altogether\n\t\t\t\t// (i.e. wasn't claimed by the other list)\n\t\t\t\t// then we need to supply an outro\n\t\t\t\titems.delete(params.key);\n\t\t\t\treturn fallback && fallback(node, params, intro);\n\t\t\t};\n\t\t};\n\t}\n\treturn [transition(to_send, to_receive, false), transition(to_receive, to_send, true)];\n}\n"], "mappings": ";;;;;;;;;;;;;;AAWO,SAAS,KACf,MACA,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,YAAY,SAAS,GAAG,UAAU,EAAE,IAAI,CAAC,GAC9E;AACD,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,IAAI,MAAM,WAAW,SAAS,KAAK,MAAM;AAC/C,QAAM,KAAK,kBAAkB,IAAI;AACjC,QAAM,CAAC,OAAO,IAAI,IAAI,eAAe,MAAM;AAC3C,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,IAAI,MAAM,YAAY,iBAAiB,KAAK,CAAC,aAAa,CAAC,SAAS,IAAI,KAAK,GAAG,IAAI;AAAA,EAC3F;AACD;AAUO,SAAS,KAAK,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,SAAO,IAAI,CAAC,GAAG;AAC/E,QAAM,IAAI,CAAC,iBAAiB,IAAI,EAAE;AAClC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,MAAM,YAAY,IAAI,CAAC;AAAA,EAC9B;AACD;AAUO,SAAS,IACf,MACA,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,UAAU,IAAI,GAAG,IAAI,GAAG,UAAU,EAAE,IAAI,CAAC,GAC9E;AACD,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,KAAK,kBAAkB,IAAI;AACjC,QAAM,CAAC,QAAQ,KAAK,IAAI,eAAe,CAAC;AACxC,QAAM,CAAC,QAAQ,KAAK,IAAI,eAAe,CAAC;AACxC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AAAA,gBACD,SAAS,eAAe,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,IAAI,KAAK,MAAM,GAAG,KAAK;AAAA,cAC9E,iBAAiB,KAAK,CAAC;AAAA,EACpC;AACD;AAUO,SAAS,MAAM,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,UAAU,OAAO,IAAI,IAAI,CAAC,GAAG;AAC9F,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,UAAU,CAAC,MAAM;AACvB,QAAM,mBAAmB,SAAS,MAAM,WAAW;AACnD,QAAM,yBAAyB,WAAW,MAAM,gBAAgB,CAAC;AACjE,QAAM,uBAAuB,SAAS,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,QAAQ,OAAO;AAChF,QAAM,mCAAmC,qBAAqB;AAAA,IAC7D,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAAA,EAC1C;AACA,QAAM,sBAAsB,WAAW,MAAM,UAAU,iCAAiC,CAAC,CAAC,EAAE,CAAC;AAC7F,QAAM,oBAAoB,WAAW,MAAM,UAAU,iCAAiC,CAAC,CAAC,EAAE,CAAC;AAC3F,QAAM,qBAAqB,WAAW,MAAM,SAAS,iCAAiC,CAAC,CAAC,EAAE,CAAC;AAC3F,QAAM,mBAAmB,WAAW,MAAM,SAAS,iCAAiC,CAAC,CAAC,EAAE,CAAC;AACzF,QAAM,2BAA2B;AAAA,IAChC,MAAM,SAAS,iCAAiC,CAAC,CAAC,OAAO;AAAA,EAC1D;AACA,QAAM,yBAAyB;AAAA,IAC9B,MAAM,SAAS,iCAAiC,CAAC,CAAC,OAAO;AAAA,EAC1D;AACA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,MACL,6BACY,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,OAAO,IACtC,gBAAgB,KAAK,IAAI,sBAAsB,cACvC,qBAAqB,CAAC,CAAC,KAAK,IAAI,mBAAmB,cACnD,qBAAqB,CAAC,CAAC,KAAK,IAAI,iBAAiB,aAClD,qBAAqB,CAAC,CAAC,KAAK,IAAI,kBAAkB,aAClD,qBAAqB,CAAC,CAAC,KAAK,IAAI,gBAAgB,aAChD,qBAAqB,CAAC,CAAC,WAAW,IAAI,wBAAwB,aAC9D,qBAAqB,CAAC,CAAC,WAAW,IAAI,sBAAsB;AAAA,EACxE;AACD;AAUO,SAAS,MACf,MACA,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,UAAU,QAAQ,GAAG,UAAU,EAAE,IAAI,CAAC,GAC3E;AACD,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,kBAAkB,IAAI;AACjC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,IAAI,MAAM;AAAA,gBACF,SAAS,UAAU,IAAI,KAAK,CAAC;AAAA,cAC/B,iBAAiB,KAAK,CAAC;AAAA;AAAA,EAEpC;AACD;AAUO,SAAS,KAAK,MAAM,EAAE,QAAQ,GAAG,OAAO,UAAU,SAAS,WAAW,IAAI,CAAC,GAAG;AACpF,MAAI,MAAM,KAAK,eAAe;AAC9B,QAAM,QAAQ,iBAAiB,IAAI;AACnC,MAAI,MAAM,kBAAkB,QAAQ;AACnC,WAAO,SAAS,MAAM,WAAW;AAAA,EAClC;AACA,MAAI,aAAa,QAAW;AAC3B,QAAI,UAAU,QAAW;AACxB,iBAAW;AAAA,IACZ,OAAO;AACN,iBAAW,MAAM;AAAA,IAClB;AAAA,EACD,WAAW,OAAO,aAAa,YAAY;AAC1C,eAAW,SAAS,GAAG;AAAA,EACxB;AACA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AAAA,uBACM,GAAG;AAAA,wBACF,IAAI,GAAG;AAAA;AAAA,EAE9B;AACD;AAWO,SAAS,UAAU,EAAE,UAAU,GAAG,SAAS,GAAG;AAEpD,QAAM,aAAa,oBAAI,IAAI;AAE3B,QAAM,UAAU,oBAAI,IAAI;AAOxB,WAASA,WAAU,WAAW,MAAM,QAAQ;AAC3C,UAAM;AAAA,MACL,QAAQ;AAAA,MACR,WAAW,CAACC,OAAM,KAAK,KAAKA,EAAC,IAAI;AAAA,MACjC,SAAS;AAAA,IACV,IAAI,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG,MAAM;AACvC,UAAM,OAAO,UAAU,sBAAsB;AAC7C,UAAM,KAAK,KAAK,sBAAsB;AACtC,UAAM,KAAK,KAAK,OAAO,GAAG;AAC1B,UAAM,KAAK,KAAK,MAAM,GAAG;AACzB,UAAM,KAAK,KAAK,QAAQ,GAAG;AAC3B,UAAM,KAAK,KAAK,SAAS,GAAG;AAC5B,UAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACrC,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,UAAM,UAAU,CAAC,MAAM;AACvB,WAAO;AAAA,MACN;AAAA,MACA,UAAU,YAAY,QAAQ,IAAI,SAAS,CAAC,IAAI;AAAA,MAChD;AAAA,MACA,KAAK,CAAC,GAAG,MAAM;AAAA,eACH,IAAI,OAAO;AAAA;AAAA,iBAET,SAAS,cAAc,IAAI,EAAE,MAAM,IAAI,EAAE,aAAa,KAAK,IAAI,KAAK,EAAE,KACnF,KAAK,IAAI,KAAK,EACf;AAAA;AAAA,IAED;AAAA,EACD;AAQA,WAAS,WAAW,OAAO,cAAc,OAAO;AAC/C,WAAO,CAAC,MAAM,WAAW;AACxB,YAAM,IAAI,OAAO,KAAK,IAAI;AAC1B,aAAO,MAAM;AACZ,YAAI,aAAa,IAAI,OAAO,GAAG,GAAG;AACjC,gBAAM,aAAa,aAAa,IAAI,OAAO,GAAG;AAC9C,uBAAa,OAAO,OAAO,GAAG;AAC9B,iBAAOD,WAAU,YAAY,MAAM,MAAM;AAAA,QAC1C;AAIA,cAAM,OAAO,OAAO,GAAG;AACvB,eAAO,YAAY,SAAS,MAAM,QAAQ,KAAK;AAAA,MAChD;AAAA,IACD;AAAA,EACD;AACA,SAAO,CAAC,WAAW,SAAS,YAAY,KAAK,GAAG,WAAW,YAAY,SAAS,IAAI,CAAC;AACtF;", "names": ["crossfade", "d"]}