<script>
  import { onMount } from 'svelte';
  import Button from '../components/ui/Button.svelte';
  import Card from '../components/ui/Card.svelte';
  import { logger } from '../utils/logger.js';
  
  let isGenerating = false;
  let generatedOutline = '';
  
  // 表单数据
  let formData = {
    title: '',
    genre: '',
    theme: '',
    wordCount: '',
    description: ''
  };
  
  onMount(() => {
    logger.info('OUTLINE_GENERATOR_PAGE_LOADED');
  });
  
  async function generateOutline() {
    if (!formData.title || !formData.genre) {
      alert('请填写小说标题和类型');
      return;
    }
    
    isGenerating = true;
    logger.info('OUTLINE_GENERATION_START', { title: formData.title });
    
    try {
      // 这里将来会调用AI服务生成大纲
      // 现在使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      generatedOutline = `《${formData.title}》大纲

第一章：开端
- 主角登场，展现平凡生活
- 意外事件发生，改变主角命运
- 引出核心冲突

第二章：发展
- 主角开始适应新环境
- 遇到重要配角
- 初步展现主角能力

第三章：转折
- 遭遇第一个重大挫折
- 主角成长和觉醒
- 揭示更大的阴谋

第四章：高潮
- 最终对决准备
- 主角全力以赴
- 解决核心冲突

第五章：结局
- 冲突解决
- 主角成长完成
- 为续集埋下伏笔`;
      
      logger.success('OUTLINE_GENERATION_SUCCESS');
      
    } catch (error) {
      logger.error('OUTLINE_GENERATION_ERROR', { error: error.message });
      alert('大纲生成失败，请重试');
    } finally {
      isGenerating = false;
    }
  }
  
  function saveOutline() {
    if (!generatedOutline) {
      alert('请先生成大纲');
      return;
    }
    
    // 这里将来会保存到数据库
    logger.info('OUTLINE_SAVED', { title: formData.title });
    alert('大纲已保存');
  }
</script>

<div class="outline-generator">
  <div class="page-header">
    <h1>大纲生成</h1>
    <p>使用AI智能生成小说大纲，为您的创作提供结构化指导</p>
  </div>

  <div class="content-grid">
    <div class="form-section">
      <Card>
        <div class="form-content">
          <h2>小说信息</h2>
          
          <div class="form-group">
            <label for="title">小说标题 *</label>
            <input 
              id="title"
              type="text" 
              bind:value={formData.title}
              placeholder="请输入小说标题"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="genre">小说类型 *</label>
            <select id="genre" bind:value={formData.genre} required>
              <option value="">请选择类型</option>
              <option value="fantasy">玄幻</option>
              <option value="urban">都市</option>
              <option value="historical">历史</option>
              <option value="scifi">科幻</option>
              <option value="romance">言情</option>
              <option value="martial">武侠</option>
              <option value="game">游戏</option>
              <option value="military">军事</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="theme">主题思想</label>
            <input 
              id="theme"
              type="text" 
              bind:value={formData.theme}
              placeholder="如：成长、复仇、爱情等"
            />
          </div>
          
          <div class="form-group">
            <label for="wordCount">预计字数</label>
            <select id="wordCount" bind:value={formData.wordCount}>
              <option value="">请选择字数</option>
              <option value="short">短篇（1-5万字）</option>
              <option value="medium">中篇（5-20万字）</option>
              <option value="long">长篇（20-100万字）</option>
              <option value="series">系列（100万字以上）</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="description">简介描述</label>
            <textarea 
              id="description"
              bind:value={formData.description}
              placeholder="请简要描述小说的背景、主角、主要情节等"
              rows="4"
            ></textarea>
          </div>
          
          <div class="form-actions">
            <Button 
              variant="primary" 
              icon="auto_awesome"
              loading={isGenerating}
              on:click={generateOutline}
              fullWidth
            >
              {isGenerating ? '生成中...' : '生成大纲'}
            </Button>
          </div>
        </div>
      </Card>
    </div>

    <div class="result-section">
      <Card>
        <div class="result-content">
          <div class="result-header">
            <h2>生成结果</h2>
            {#if generatedOutline}
              <Button 
                variant="outline" 
                icon="save"
                on:click={saveOutline}
              >
                保存大纲
              </Button>
            {/if}
          </div>
          
          {#if generatedOutline}
            <div class="outline-content">
              <pre>{generatedOutline}</pre>
            </div>
          {:else}
            <div class="empty-state">
              <div class="empty-icon">
                <span class="material-symbols-outlined">description</span>
              </div>
              <p>填写左侧信息后点击"生成大纲"开始创作</p>
            </div>
          {/if}
        </div>
      </Card>
    </div>
  </div>
</div>

<style>
  .outline-generator {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--md-sys-spacing-lg);
  }

  .page-header {
    margin-bottom: var(--md-sys-spacing-xl);
  }

  .page-header h1 {
    font-family: var(--md-sys-typescale-headline-large-font);
    font-size: var(--md-sys-typescale-headline-large-size);
    font-weight: var(--md-sys-typescale-headline-large-weight);
    color: var(--md-sys-color-on-background);
    margin-bottom: var(--md-sys-spacing-sm);
  }

  .page-header p {
    font-family: var(--md-sys-typescale-body-large-font);
    font-size: var(--md-sys-typescale-body-large-size);
    color: var(--md-sys-color-on-surface-variant);
  }

  .content-grid {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--md-sys-spacing-xl);
  }

  :global(.form-section .card),
  :global(.result-section .card) {
    height: fit-content;
  }

  .form-content,
  .result-content {
    padding: var(--md-sys-spacing-lg);
  }

  .form-content h2,
  .result-content h2 {
    font-family: var(--md-sys-typescale-headline-medium-font);
    font-size: var(--md-sys-typescale-headline-medium-size);
    font-weight: var(--md-sys-typescale-headline-medium-weight);
    color: var(--md-sys-color-on-surface);
    margin-bottom: var(--md-sys-spacing-lg);
  }

  .form-group {
    margin-bottom: var(--md-sys-spacing-lg);
  }

  .form-group label {
    display: block;
    font-family: var(--md-sys-typescale-body-medium-font);
    font-size: var(--md-sys-typescale-body-medium-size);
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
    margin-bottom: var(--md-sys-spacing-sm);
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--md-sys-color-outline-variant);
    border-radius: var(--md-sys-shape-corner-sm);
    background: var(--md-sys-color-surface);
    color: var(--md-sys-color-on-surface);
    font-family: var(--md-sys-typescale-body-large-font);
    font-size: var(--md-sys-typescale-body-large-size);
    transition: border-color 0.2s ease;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: var(--md-sys-color-primary);
    box-shadow: 0 0 0 1px var(--md-sys-color-primary);
  }

  .form-group textarea {
    resize: vertical;
    min-height: 100px;
  }

  .form-actions {
    margin-top: var(--md-sys-spacing-xl);
  }

  .result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--md-sys-spacing-lg);
  }

  .outline-content {
    background: var(--md-sys-color-surface-container-high);
    border-radius: var(--md-sys-shape-corner-sm);
    padding: var(--md-sys-spacing-lg);
  }

  .outline-content pre {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: var(--md-sys-color-on-surface);
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--md-sys-spacing-xxl);
    text-align: center;
  }

  .empty-icon {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--md-sys-color-surface-container-high);
    border-radius: var(--md-sys-shape-corner-full);
    margin-bottom: var(--md-sys-spacing-lg);
  }

  .empty-icon span {
    font-size: 32px;
    color: var(--md-sys-color-on-surface-variant);
  }

  .empty-state p {
    color: var(--md-sys-color-on-surface-variant);
    font-family: var(--md-sys-typescale-body-large-font);
    font-size: var(--md-sys-typescale-body-large-size);
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .content-grid {
      grid-template-columns: 1fr;
      gap: var(--md-sys-spacing-lg);
    }
  }

  @media (max-width: 768px) {
    .outline-generator {
      padding: var(--md-sys-spacing-md);
    }

    .form-content,
    .result-content {
      padding: var(--md-sys-spacing-md);
    }
  }
</style>
