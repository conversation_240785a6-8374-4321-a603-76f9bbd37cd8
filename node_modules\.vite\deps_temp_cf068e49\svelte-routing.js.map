{"version": 3, "sources": ["../../svelte-routing/src/contexts.js", "../../svelte-routing/src/utils.js", "../../svelte-routing/src/Link.svelte", "../../svelte-routing/src/Route.svelte", "../../svelte-routing/src/history.js", "../../svelte-routing/src/Router.svelte", "../../svelte-routing/src/actions.js"], "sourcesContent": ["import { getContext } from \"svelte\";\n\nexport const LOCATION = {};\nexport const ROUTER = {};\nexport const HISTORY = {};\n\nexport const useLocation = () => getContext(LOCATION);\nexport const useRouter = () => getContext(ROUTER);\nexport const useHistory = () => getContext(HISTORY);\n", "/**\n * Adapted from https://github.com/reach/router/blob/b60e6dd781d5d3a4bdaaf4de665649c0f6a7e78d/src/lib/utils.js\n * https://github.com/reach/router/blob/master/LICENSE\n */\n\nconst PARAM = /^:(.+)/;\nconst SEGMENT_POINTS = 4;\nconst STATIC_POINTS = 3;\nconst DYNAMIC_POINTS = 2;\nconst SPLAT_PENALTY = 1;\nconst ROOT_POINTS = 1;\n\n/**\n * Split up the URI into segments delimited by `/`\n * Strip starting/ending `/`\n * @param {string} uri\n * @return {string[]}\n */\nconst segmentize = (uri) => uri.replace(/(^\\/+|\\/+$)/g, \"\").split(\"/\");\n/**\n * Strip `str` of potential start and end `/`\n * @param {string} string\n * @return {string}\n */\nconst stripSlashes = (string) => string.replace(/(^\\/+|\\/+$)/g, \"\");\n/**\n * Score a route depending on how its individual segments look\n * @param {object} route\n * @param {number} index\n * @return {object}\n */\nconst rankRoute = (route, index) => {\n    const score = route.default\n        ? 0\n        : segmentize(route.path).reduce((score, segment) => {\n              score += SEGMENT_POINTS;\n\n              if (segment === \"\") {\n                  score += ROOT_POINTS;\n              } else if (PARAM.test(segment)) {\n                  score += DYNAMIC_POINTS;\n              } else if (segment[0] === \"*\") {\n                  score -= SEGMENT_POINTS + SPLAT_PENALTY;\n              } else {\n                  score += STATIC_POINTS;\n              }\n\n              return score;\n          }, 0);\n\n    return { route, score, index };\n};\n/**\n * Give a score to all routes and sort them on that\n * If two routes have the exact same score, we go by index instead\n * @param {object[]} routes\n * @return {object[]}\n */\nconst rankRoutes = (routes) =>\n    routes\n        .map(rankRoute)\n        .sort((a, b) =>\n            a.score < b.score ? 1 : a.score > b.score ? -1 : a.index - b.index\n        );\n/**\n * Ranks and picks the best route to match. Each segment gets the highest\n * amount of points, then the type of segment gets an additional amount of\n * points where\n *\n *  static > dynamic > splat > root\n *\n * This way we don't have to worry about the order of our routes, let the\n * computers do it.\n *\n * A route looks like this\n *\n *  { path, default, value }\n *\n * And a returned match looks like:\n *\n *  { route, params, uri }\n *\n * @param {object[]} routes\n * @param {string} uri\n * @return {?object}\n */\nconst pick = (routes, uri) => {\n    let match;\n    let default_;\n\n    const [uriPathname] = uri.split(\"?\");\n    const uriSegments = segmentize(uriPathname);\n    const isRootUri = uriSegments[0] === \"\";\n    const ranked = rankRoutes(routes);\n\n    for (let i = 0, l = ranked.length; i < l; i++) {\n        const route = ranked[i].route;\n        let missed = false;\n\n        if (route.default) {\n            default_ = {\n                route,\n                params: {},\n                uri,\n            };\n            continue;\n        }\n\n        const routeSegments = segmentize(route.path);\n        const params = {};\n        const max = Math.max(uriSegments.length, routeSegments.length);\n        let index = 0;\n\n        for (; index < max; index++) {\n            const routeSegment = routeSegments[index];\n            const uriSegment = uriSegments[index];\n\n            if (routeSegment && routeSegment[0] === \"*\") {\n                // Hit a splat, just grab the rest, and return a match\n                // uri:   /files/documents/work\n                // route: /files/* or /files/*splatname\n                const splatName =\n                    routeSegment === \"*\" ? \"*\" : routeSegment.slice(1);\n\n                params[splatName] = uriSegments\n                    .slice(index)\n                    .map(decodeURIComponent)\n                    .join(\"/\");\n                break;\n            }\n\n            if (typeof uriSegment === \"undefined\") {\n                // URI is shorter than the route, no match\n                // uri:   /users\n                // route: /users/:userId\n                missed = true;\n                break;\n            }\n\n            const dynamicMatch = PARAM.exec(routeSegment);\n\n            if (dynamicMatch && !isRootUri) {\n                const value = decodeURIComponent(uriSegment);\n                params[dynamicMatch[1]] = value;\n            } else if (routeSegment !== uriSegment) {\n                // Current segments don't match, not dynamic, not splat, so no match\n                // uri:   /users/123/settings\n                // route: /users/:id/profile\n                missed = true;\n                break;\n            }\n        }\n\n        if (!missed) {\n            match = {\n                route,\n                params,\n                uri: \"/\" + uriSegments.slice(0, index).join(\"/\"),\n            };\n            break;\n        }\n    }\n\n    return match || default_ || null;\n};\n/**\n * Add the query to the pathname if a query is given\n * @param {string} pathname\n * @param {string} [query]\n * @return {string}\n */\nconst addQuery = (pathname, query) => pathname + (query ? `?${query}` : \"\");\n/**\n * Resolve URIs as though every path is a directory, no files. Relative URIs\n * in the browser can feel awkward because not only can you be \"in a directory\",\n * you can be \"at a file\", too. For example:\n *\n *  browserSpecResolve('foo', '/bar/') => /bar/foo\n *  browserSpecResolve('foo', '/bar') => /foo\n *\n * But on the command line of a file system, it's not as complicated. You can't\n * `cd` from a file, only directories. This way, links have to know less about\n * their current path. To go deeper you can do this:\n *\n *  <Link to=\"deeper\"/>\n *  // instead of\n *  <Link to=`{${props.uri}/deeper}`/>\n *\n * Just like `cd`, if you want to go deeper from the command line, you do this:\n *\n *  cd deeper\n *  # not\n *  cd $(pwd)/deeper\n *\n * By treating every path as a directory, linking to relative paths should\n * require less contextual information and (fingers crossed) be more intuitive.\n * @param {string} to\n * @param {string} base\n * @return {string}\n */\nconst resolve = (to, base) => {\n    // /foo/bar, /baz/qux => /foo/bar\n    if (to.startsWith(\"/\")) return to;\n\n    const [toPathname, toQuery] = to.split(\"?\");\n    const [basePathname] = base.split(\"?\");\n    const toSegments = segmentize(toPathname);\n    const baseSegments = segmentize(basePathname);\n\n    // ?a=b, /users?b=c => /users?a=b\n    if (toSegments[0] === \"\") return addQuery(basePathname, toQuery);\n\n    // profile, /users/789 => /users/789/profile\n\n    if (!toSegments[0].startsWith(\".\")) {\n        const pathname = baseSegments.concat(toSegments).join(\"/\");\n        return addQuery((basePathname === \"/\" ? \"\" : \"/\") + pathname, toQuery);\n    }\n\n    // ./       , /users/123 => /users/123\n    // ../      , /users/123 => /users\n    // ../..    , /users/123 => /\n    // ../../one, /a/b/c/d   => /a/b/one\n    // .././one , /a/b/c/d   => /a/b/c/one\n    const allSegments = baseSegments.concat(toSegments);\n    const segments = [];\n\n    allSegments.forEach((segment) => {\n        if (segment === \"..\") segments.pop();\n        else if (segment !== \".\") segments.push(segment);\n    });\n\n    return addQuery(\"/\" + segments.join(\"/\"), toQuery);\n};\n/**\n * Combines the `basepath` and the `path` into one path.\n * @param {string} basepath\n * @param {string} path\n */\nconst combinePaths = (basepath, path) =>\n    `${stripSlashes(\n        path === \"/\"\n            ? basepath\n            : `${stripSlashes(basepath)}/${stripSlashes(path)}`\n    )}/`;\n/**\n * Decides whether a given `event` should result in a navigation or not.\n * @param {object} event\n */\nconst shouldNavigate = (event) =>\n    !event.defaultPrevented &&\n    event.button === 0 &&\n    !(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n\n// svelte seems to kill anchor.host value in ie11, so fall back to checking href\nconst hostMatches = (anchor) => {\n    const host = location.host;\n    return (\n        anchor.host === host ||\n        anchor.href.indexOf(`https://${host}`) === 0 ||\n        anchor.href.indexOf(`http://${host}`) === 0\n    );\n};\n\nconst canUseDOM = () =>\n    typeof window !== \"undefined\" &&\n    \"document\" in window &&\n    \"location\" in window;\n\nexport {\n    stripSlashes,\n    pick,\n    resolve,\n    combinePaths,\n    shouldNavigate,\n    hostMatches,\n    canUseDOM,\n};\n", null, null, "/**\n * Adapted from https://github.com/reach/router/blob/b60e6dd781d5d3a4bdaaf4de665649c0f6a7e78d/src/lib/history.js\n * https://github.com/reach/router/blob/master/LICENSE\n */\nimport { canUseDOM } from \"./utils\";\n\nconst getLocation = (source) => {\n    return {\n        ...source.location,\n        state: source.history.state,\n        key: (source.history.state && source.history.state.key) || \"initial\",\n    };\n};\nconst createHistory = (source) => {\n    const listeners = [];\n    let location = getLocation(source);\n\n    return {\n        get location() {\n            return location;\n        },\n\n        listen(listener) {\n            listeners.push(listener);\n\n            const popstateListener = () => {\n                location = getLocation(source);\n                listener({ location, action: \"POP\" });\n            };\n\n            source.addEventListener(\"popstate\", popstateListener);\n\n            return () => {\n                source.removeEventListener(\"popstate\", popstateListener);\n                const index = listeners.indexOf(listener);\n                listeners.splice(index, 1);\n            };\n        },\n\n        navigate(to, { state, replace = false, preserveScroll = false, blurActiveElement = true } = {}) {\n            state = { ...state, key: Date.now() + \"\" };\n            // try...catch iOS Safari limits to 100 pushState calls\n            try {\n                if (replace) source.history.replaceState(state, \"\", to);\n                else source.history.pushState(state, \"\", to);\n            } catch (e) {\n                source.location[replace ? \"replace\" : \"assign\"](to);\n            }\n            location = getLocation(source);\n            listeners.forEach((listener) =>\n                listener({ location, action: \"PUSH\", preserveScroll })\n            );\n            if(blurActiveElement) document.activeElement.blur();\n        },\n    };\n};\n// Stores history entries in memory for testing or other platforms like Native\nconst createMemorySource = (initialPathname = \"/\") => {\n    let index = 0;\n    const stack = [{ pathname: initialPathname, search: \"\" }];\n    const states = [];\n\n    return {\n        get location() {\n            return stack[index];\n        },\n        addEventListener(name, fn) {},\n        removeEventListener(name, fn) {},\n        history: {\n            get entries() {\n                return stack;\n            },\n            get index() {\n                return index;\n            },\n            get state() {\n                return states[index];\n            },\n            pushState(state, _, uri) {\n                const [pathname, search = \"\"] = uri.split(\"?\");\n                index++;\n                stack.push({ pathname, search });\n                states.push(state);\n            },\n            replaceState(state, _, uri) {\n                const [pathname, search = \"\"] = uri.split(\"?\");\n                stack[index] = { pathname, search };\n                states[index] = state;\n            },\n        },\n    };\n};\n// Global history uses window.history as the source if available,\n// otherwise a memory history\nconst globalHistory = createHistory(\n    canUseDOM() ? window : createMemorySource()\n);\nconst { navigate } = globalHistory;\n\nexport { globalHistory, navigate, createHistory, createMemorySource };\n", null, "import { navigate } from \"./history.js\";\nimport { hostMatches, shouldNavi<PERSON> } from \"./utils.js\";\n\n/**\n * A link action that can be added to <a href=\"\"> tags rather\n * than using the <Link> component.\n *\n * Example:\n * ```html\n * <a href=\"/post/{postId}\" use:link>{post.title}</a>\n * ```\n */\nconst link = (node) => {\n    const onClick = (event) => {\n        const anchor = event.currentTarget;\n\n        if (\n            (anchor.target === \"\" || anchor.target === \"_self\") &&\n            hostMatches(anchor) &&\n            shouldNavigate(event)\n        ) {\n            event.preventDefault();\n            navigate(anchor.pathname + anchor.search, {\n                replace: anchor.hasAttribute(\"replace\"),\n                preserveScroll: anchor.hasAttribute(\"preserveScroll\"),\n            });\n        }\n    };\n\n    node.addEventListener(\"click\", onClick);\n\n    return {\n        destroy() {\n            node.removeEventListener(\"click\", onClick);\n        },\n    };\n};\n/**\n * An action to be added at a root element of your application to\n * capture all relative links and push them onto the history stack.\n *\n * Example:\n * ```html\n * <div use:links>\n *   <Router>\n *     <Route path=\"/\" component={Home} />\n *     <Route path=\"/p/:projectId/:docId?\" component={ProjectScreen} />\n *     {#each projects as project}\n *       <a href=\"/p/{project.id}\">{project.title}</a>\n *     {/each}\n *   </Router>\n * </div>\n * ```\n */\nconst links = (node) => {\n    const findClosest = (tagName, el) => {\n        while (el && el.tagName !== tagName) el = el.parentNode;\n        return el;\n    };\n\n    const onClick = (event) => {\n        const anchor = findClosest(\"A\", event.target);\n        if (\n            anchor &&\n            (anchor.target === \"\" || anchor.target === \"_self\") &&\n            hostMatches(anchor) &&\n            shouldNavigate(event) &&\n            !anchor.hasAttribute(\"noroute\")\n        ) {\n            event.preventDefault();\n            navigate(anchor.pathname + anchor.search, {\n                replace: anchor.hasAttribute(\"replace\"),\n                preserveScroll: anchor.hasAttribute(\"preserveScroll\"),\n            });\n        }\n    };\n\n    node.addEventListener(\"click\", onClick);\n\n    return {\n        destroy() {\n            node.removeEventListener(\"click\", onClick);\n        },\n    };\n};\n\nexport { link, links };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAM,WAAW,CAAC;AAClB,IAAM,SAAS,CAAC;AAChB,IAAM,UAAU,CAAC;AAEjB,IAAM,cAAc,MAAM,WAAW,QAAQ;AAC7C,IAAM,YAAY,MAAM,WAAW,MAAM;AACzC,IAAM,aAAa,MAAM,WAAW,OAAO;;;ACHlD,IAAM,QAAQ;AACd,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AAQpB,IAAM,aAAa,CAAC,QAAQ,IAAI,QAAQ,gBAAgB,EAAE,EAAE,MAAM,GAAG;AAMrE,IAAM,eAAe,CAAC,WAAW,OAAO,QAAQ,gBAAgB,EAAE;AAOlE,IAAM,YAAY,CAAC,OAAO,UAAU;AAChC,QAAM,QAAQ,MAAM,UACd,IACA,WAAW,MAAM,IAAI,EAAE,OAAO,CAACA,QAAO,YAAY;AAC9C,IAAAA,UAAS;AAET,QAAI,YAAY,IAAI;AAChB,MAAAA,UAAS;AAAA,IACb,WAAW,MAAM,KAAK,OAAO,GAAG;AAC5B,MAAAA,UAAS;AAAA,IACb,WAAW,QAAQ,CAAC,MAAM,KAAK;AAC3B,MAAAA,UAAS,iBAAiB;AAAA,IAC9B,OAAO;AACH,MAAAA,UAAS;AAAA,IACb;AAEA,WAAOA;AAAA,EACX,GAAG,CAAC;AAEV,SAAO,EAAE,OAAO,OAAO,MAAM;AACjC;AAOA,IAAM,aAAa,CAAC,WAChB,OACK,IAAI,SAAS,EACb;AAAA,EAAK,CAAC,GAAG,MACN,EAAE,QAAQ,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;AACjE;AAuBR,IAAM,OAAO,CAAC,QAAQ,QAAQ;AAC1B,MAAI;AACJ,MAAI;AAEJ,QAAM,CAAC,WAAW,IAAI,IAAI,MAAM,GAAG;AACnC,QAAM,cAAc,WAAW,WAAW;AAC1C,QAAM,YAAY,YAAY,CAAC,MAAM;AACrC,QAAM,SAAS,WAAW,MAAM;AAEhC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC3C,UAAM,QAAQ,OAAO,CAAC,EAAE;AACxB,QAAI,SAAS;AAEb,QAAI,MAAM,SAAS;AACf,iBAAW;AAAA,QACP;AAAA,QACA,QAAQ,CAAC;AAAA,QACT;AAAA,MACJ;AACA;AAAA,IACJ;AAEA,UAAM,gBAAgB,WAAW,MAAM,IAAI;AAC3C,UAAM,SAAS,CAAC;AAChB,UAAM,MAAM,KAAK,IAAI,YAAY,QAAQ,cAAc,MAAM;AAC7D,QAAI,QAAQ;AAEZ,WAAO,QAAQ,KAAK,SAAS;AACzB,YAAM,eAAe,cAAc,KAAK;AACxC,YAAM,aAAa,YAAY,KAAK;AAEpC,UAAI,gBAAgB,aAAa,CAAC,MAAM,KAAK;AAIzC,cAAM,YACF,iBAAiB,MAAM,MAAM,aAAa,MAAM,CAAC;AAErD,eAAO,SAAS,IAAI,YACf,MAAM,KAAK,EACX,IAAI,kBAAkB,EACtB,KAAK,GAAG;AACb;AAAA,MACJ;AAEA,UAAI,OAAO,eAAe,aAAa;AAInC,iBAAS;AACT;AAAA,MACJ;AAEA,YAAM,eAAe,MAAM,KAAK,YAAY;AAE5C,UAAI,gBAAgB,CAAC,WAAW;AAC5B,cAAM,QAAQ,mBAAmB,UAAU;AAC3C,eAAO,aAAa,CAAC,CAAC,IAAI;AAAA,MAC9B,WAAW,iBAAiB,YAAY;AAIpC,iBAAS;AACT;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,CAAC,QAAQ;AACT,cAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA,KAAK,MAAM,YAAY,MAAM,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,MACnD;AACA;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO,SAAS,YAAY;AAChC;AAOA,IAAM,WAAW,CAAC,UAAU,UAAU,YAAY,QAAQ,IAAI,KAAK,KAAK;AA6BxE,IAAM,UAAU,CAAC,IAAI,SAAS;AAE1B,MAAI,GAAG,WAAW,GAAG,EAAG,QAAO;AAE/B,QAAM,CAAC,YAAY,OAAO,IAAI,GAAG,MAAM,GAAG;AAC1C,QAAM,CAAC,YAAY,IAAI,KAAK,MAAM,GAAG;AACrC,QAAM,aAAa,WAAW,UAAU;AACxC,QAAM,eAAe,WAAW,YAAY;AAG5C,MAAI,WAAW,CAAC,MAAM,GAAI,QAAO,SAAS,cAAc,OAAO;AAI/D,MAAI,CAAC,WAAW,CAAC,EAAE,WAAW,GAAG,GAAG;AAChC,UAAM,WAAW,aAAa,OAAO,UAAU,EAAE,KAAK,GAAG;AACzD,WAAO,UAAU,iBAAiB,MAAM,KAAK,OAAO,UAAU,OAAO;AAAA,EACzE;AAOA,QAAM,cAAc,aAAa,OAAO,UAAU;AAClD,QAAM,WAAW,CAAC;AAElB,cAAY,QAAQ,CAAC,YAAY;AAC7B,QAAI,YAAY,KAAM,UAAS,IAAI;AAAA,aAC1B,YAAY,IAAK,UAAS,KAAK,OAAO;AAAA,EACnD,CAAC;AAED,SAAO,SAAS,MAAM,SAAS,KAAK,GAAG,GAAG,OAAO;AACrD;AAMA,IAAM,eAAe,CAAC,UAAU,SAC5B,GAAG;AAAA,EACC,SAAS,MACH,WACA,GAAG,aAAa,QAAQ,CAAC,IAAI,aAAa,IAAI,CAAC;AACzD,CAAC;AAKL,IAAM,iBAAiB,CAAC,UACpB,CAAC,MAAM,oBACP,MAAM,WAAW,KACjB,EAAE,MAAM,WAAW,MAAM,UAAU,MAAM,WAAW,MAAM;AAG9D,IAAM,cAAc,CAAC,WAAW;AAC5B,QAAM,OAAO,SAAS;AACtB,SACI,OAAO,SAAS,QAChB,OAAO,KAAK,QAAQ,WAAW,IAAI,EAAE,MAAM,KAC3C,OAAO,KAAK,QAAQ,UAAU,IAAI,EAAE,MAAM;AAElD;AAEA,IAAM,YAAY,MACd,OAAO,WAAW,eAClB,cAAc,UACd,cAAc;;;;;;;AC3NE,IAAW,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;MALb,IAAW,CAAA;MAAA;;IAErB,IAAK,CAAA;;IACL,IAAW,CAAA;;;;;;;;;;;;;;;;;AALnB,iBAQG,QAAA,GAAA,MAAA;;;;;;;;;;UALW,IAAO,CAAA;UAAA;UAAA;UAAA;UAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UADHC,KAAW,CAAA;UAAA;;;QAErBA,KAAK,CAAA;;;QACLA,KAAW,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAzCJ,KAAK,IAAG,IAAA;QACR,UAAU,MAAK,IAAA;QACf,QAAK,CAAA,EAAA,IAAA;QACL,WAAQ,OAAA,CAAA,GAAA,IAAA;QACR,iBAAiB,MAAK,IAAA;QAE3BC,YAAW,WAAW,QAAQ;;;UAC5B,KAAI,IAAK,WAAW,MAAM;;;UAC1B,UAAAC,UAAQ,IAAK,WAAW,OAAO;QACjC,WAAW,sBAAqB;MAElC,MAAM,oBAAoB,WAAW;QAanC,UAAW,WAAK;AAClB,aAAS,SAAS,KAAK;QACnB,eAAe,KAAK,GAAA;AACpB,YAAM,eAAc;YAGd,gBAAgB,UAAU,aAAa,QAAQ;AACrD,MAAAA,UAAS,MAAI;QAAI;QAAO,SAAS;QAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAnBxD,QAAC,cAAA,GAAE,OAAO,QAAQ,IAAI,MAAM,GAAG,CAAA;;;;AAC/B,QAAC,cAAA,IAAE,qBAAqB,UAAU,SAAS,WAAW,IAAI,CAAA;;;;AAC1D,QAAC,cAAA,IAAE,YAAY,SAAS,UAAU,QAAQ;;;;AAC1C,QAAC,cAAA,GAAE,cAAc,YAAY,SAAS,MAAS;;AAC/C,MAAC,cAAA,GAAE,QAAQ,SAAQ;MACf,UAAU;MACV;MACA;MACA;MACA,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ECyBD,IAAW,CAAA;EAAA;;;;;;;;;;;MATxBC,KAAS,CAAA;KAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EACF,IAAS,CAAA,GAAA,IAAA;;;;;;;;;;;;;;;;;;MAAT,IAAS,CAAA,MAAA,eAAA,SAAA,IAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAGL,IAAW,CAAA;;IACX,IAAU,CAAA;EAAA;;;MAFR,SAAiB,EAAA,MAAjB,mBAAmB;IAAW,IAAiB,EAAA;;;;;;;;;;;;;UACjDA,KAAW,CAAA;QAAA;;;;UACXA,KAAU,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;QAFRC,MAAAD,KAAiB,EAAA,MAAjB,gBAAAC,IAAmB;MAAWD,KAAiB,EAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;YACjDA,KAAW,CAAA;UAAA;;;;YACXA,KAAU,CAAA;UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IANzB,IAAY,CAAA;IAAI,IAAY,CAAA,EAAC;IAAU,IAAK,CAAA,KAAA,gBAAA,GAAA;;;;;;;;;;;;;;;;;;QAA5CA,KAAY,CAAA;QAAIA,KAAY,CAAA,EAAC;QAAUA,KAAK,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QApClC,OAAO,GAAE,IAAA;QACT,YAAY,KAAI,IAAA;MAEvB,cAAW,CAAA;MACX,aAAU,CAAA;UAEN,eAAe,iBAAiB,YAAW,IAAK,WAAW,MAAM;;;QAEnE,QAAK;IACP;;;IAGA,SAAS,SAAS;;AAiBtB,gBAAc,KAAK;AAEnB,YAAS,MAAA;AACL,oBAAgB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjBzB,MAAC,KAAM,gBAAgB,aAAa,UAAU,OAAK;sBAC/C,cAAc,aAAa,MAAM;cAEzB,WAAW,GAAG,MAAAE,OAAI,GAAK,KAAI,IAAK;sBACxC,aAAa,IAAI;UAEb,GAAC;YACG,EAAE,SAAQ,EAAG,WAAW,QAAQ,EAAA,cAAA,GAAG,YAAY,CAAC;YAAA,cAAA,GAC/C,YAAY,EAAC,CAAA;;AAGtB,gBAAS,KAAA,CAAO,aAAa,mBAAkB,iCAAQ,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzB3E,IAAM,cAAc,CAAC,WAAW;AAC5B,SAAO;AAAA,IACH,GAAG,OAAO;AAAA,IACV,OAAO,OAAO,QAAQ;AAAA,IACtB,KAAM,OAAO,QAAQ,SAAS,OAAO,QAAQ,MAAM,OAAQ;AAAA,EAC/D;AACJ;AACA,IAAM,gBAAgB,CAAC,WAAW;AAC9B,QAAM,YAAY,CAAC;AACnB,MAAIC,YAAW,YAAY,MAAM;AAEjC,SAAO;AAAA,IACH,IAAI,WAAW;AACX,aAAOA;AAAA,IACX;AAAA,IAEA,OAAO,UAAU;AACb,gBAAU,KAAK,QAAQ;AAEvB,YAAM,mBAAmB,MAAM;AAC3B,QAAAA,YAAW,YAAY,MAAM;AAC7B,iBAAS,EAAE,UAAAA,WAAU,QAAQ,MAAM,CAAC;AAAA,MACxC;AAEA,aAAO,iBAAiB,YAAY,gBAAgB;AAEpD,aAAO,MAAM;AACT,eAAO,oBAAoB,YAAY,gBAAgB;AACvD,cAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,kBAAU,OAAO,OAAO,CAAC;AAAA,MAC7B;AAAA,IACJ;AAAA,IAEA,SAAS,IAAI,EAAE,OAAO,UAAU,OAAO,iBAAiB,OAAO,oBAAoB,KAAK,IAAI,CAAC,GAAG;AAC5F,cAAQ,EAAE,GAAG,OAAO,KAAK,KAAK,IAAI,IAAI,GAAG;AAEzC,UAAI;AACA,YAAI,QAAS,QAAO,QAAQ,aAAa,OAAO,IAAI,EAAE;AAAA,YACjD,QAAO,QAAQ,UAAU,OAAO,IAAI,EAAE;AAAA,MAC/C,SAAS,GAAG;AACR,eAAO,SAAS,UAAU,YAAY,QAAQ,EAAE,EAAE;AAAA,MACtD;AACA,MAAAA,YAAW,YAAY,MAAM;AAC7B,gBAAU;AAAA,QAAQ,CAAC,aACf,SAAS,EAAE,UAAAA,WAAU,QAAQ,QAAQ,eAAe,CAAC;AAAA,MACzD;AACA,UAAG,kBAAmB,UAAS,cAAc,KAAK;AAAA,IACtD;AAAA,EACJ;AACJ;AAEA,IAAM,qBAAqB,CAAC,kBAAkB,QAAQ;AAClD,MAAI,QAAQ;AACZ,QAAM,QAAQ,CAAC,EAAE,UAAU,iBAAiB,QAAQ,GAAG,CAAC;AACxD,QAAM,SAAS,CAAC;AAEhB,SAAO;AAAA,IACH,IAAI,WAAW;AACX,aAAO,MAAM,KAAK;AAAA,IACtB;AAAA,IACA,iBAAiB,MAAM,IAAI;AAAA,IAAC;AAAA,IAC5B,oBAAoB,MAAM,IAAI;AAAA,IAAC;AAAA,IAC/B,SAAS;AAAA,MACL,IAAI,UAAU;AACV,eAAO;AAAA,MACX;AAAA,MACA,IAAI,QAAQ;AACR,eAAO;AAAA,MACX;AAAA,MACA,IAAI,QAAQ;AACR,eAAO,OAAO,KAAK;AAAA,MACvB;AAAA,MACA,UAAU,OAAO,GAAG,KAAK;AACrB,cAAM,CAAC,UAAU,SAAS,EAAE,IAAI,IAAI,MAAM,GAAG;AAC7C;AACA,cAAM,KAAK,EAAE,UAAU,OAAO,CAAC;AAC/B,eAAO,KAAK,KAAK;AAAA,MACrB;AAAA,MACA,aAAa,OAAO,GAAG,KAAK;AACxB,cAAM,CAAC,UAAU,SAAS,EAAE,IAAI,IAAI,MAAM,GAAG;AAC7C,cAAM,KAAK,IAAI,EAAE,UAAU,OAAO;AAClC,eAAO,KAAK,IAAI;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACJ;AAGA,IAAM,gBAAgB;AAAA,EAClB,UAAU,IAAI,SAAS,mBAAmB;AAC9C;AACA,IAAM,EAAE,SAAS,IAAI;;;;;;;;;;;;;;IC8CJ,IAAY,CAAA;IAAI,IAAY,CAAA,EAAC;;;;IAAe,IAAS,CAAA;;;;;;;;;;;;IAN/C,IAAY,CAAA;IAAI,IAAY,CAAA,EAAC;;;;IAC1B,IAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAJzB,IAAS,CAAA,EAAC;;;;;;;;;;;;;;;;;;MAAVC,KAAS,CAAA,EAAC,QAAQ,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACpB,iBAKK,QAAA,KAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAPRA,KAAc,CAAA;KAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA9HJ,WAAW,IAAG,IAAA;QACd,MAAM,KAAI,IAAA;QACV,iBAAiB,KAAI,IAAA;QACrB,UAAU,cAAa,IAAA;QAE5B,mBAAgB,CAAI,MAAM,GAAG,cAAS;UAClC,KAAK,eAAe,SAAS;gBACxB,yBAAI,QAAO,WAAU,QAAS,GAAG,GAAG,MAAM,EAAE;QAAA,QAC3C;;AAGhB,aAAW,SAAS,OAAO;QAErB,kBAAkB,WAAW,QAAQ;QACrC,gBAAgB,WAAW,MAAM;QAEjC,SAAS,SAAQ,CAAA,CAAA;;;QACjB,cAAc,SAAS,IAAI;;;MAC7B,iBAAiB;QAIfC,YACF,mBAAmB,SAAS,MAAG,EAAK,UAAU,IAAG,IAAK,QAAQ,QAAQ;;;QAMpE,OAAO,gBACP,cAAc,aACd,SAAQ,EACJ,MAAM,UACN,KAAK,SAAQ,CAAA;;;QAGjB,aAAa,QAAO,CAAE,MAAM,WAAW,GAAA,CAAA,CAAKC,OAAMC,YAAW,MAAA;SAE1DA,aAAW,QAASD;YAEjB,MAAME,UAAQ,IAAKF;YACnB,OAAO,IAAG,IAAKC;UAGjB,OAAO,MAAM,UAAUC,YAAW,MAAM,KAAK,QAAQ,SAAS,EAAE;aAC7D,MAAM,IAAG;;QAGhB,gBAAiB,WAAK;YAChB,MAAMA,UAAQ,IAAK;UACrB,KAAI,IAAK;AAKf,UAAM,QAAQ;AACd,UAAM,OAAO,aAAaA,WAAU,IAAI;eAE7B,WAAW,aAAW;UAIzB,eAAc;YAEZ,gBAAgB,KAAI,CAAE,KAAK,GAAG,UAAU,QAAQ;UAElD,eAAa;AACb,oBAAY,IAAI,aAAa;AAC7B,yBAAiB;;;AAGrB,aAAO,OAAQ,QAAE,CAAA,GAAS,IAAI,KAAK,CAAA;;;QAIrC,kBAAmB,WAAK;AAC1B,WAAO,OAAQ,QAAO,GAAG,OAAQ,OAAM,MAAM,KAAK,CAAA;;MAGlD,iBAAiB;OAuBhB,iBAAe;AAGhB,YAAO,MAAA;YACG,WAAW,QAAQ,OAAQ,WAAK;yBAClC,iBAAiB,MAAM,kBAAkB,KAAK;AAC9C,QAAAH,UAAS,IAAI,MAAM,QAAQ;;aAGxB;;AAGX,eAAW,UAAUA,SAAQ;;AAGjC,aAAW,QAAM;IACb;IACA;IACA;IACA;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvCJ,SAAC;gBACW,MAAMG,UAAQ,IAAK;AAC3B,eAAO,OAAQ,QACX,GAAG,IAAK,OACJ,OAAO,OAAO,GAAC,EAAI,MAAM,aAAaA,WAAU,EAAE,KAAK,EAAA,CAAA,CAAA,CAAA;;;;;AAQnE,SAAC;cACS,YAAY,KAAK,SAAS,UAAU,QAAQ;AAClD,oBAAY,IACR,YAAS,EAAA,GAAQ,WAAW,eAAc,IAAK,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7F3D,IAAM,OAAO,CAAC,SAAS;AACnB,QAAM,UAAU,CAAC,UAAU;AACvB,UAAM,SAAS,MAAM;AAErB,SACK,OAAO,WAAW,MAAM,OAAO,WAAW,YAC3C,YAAY,MAAM,KAClB,eAAe,KAAK,GACtB;AACE,YAAM,eAAe;AACrB,eAAS,OAAO,WAAW,OAAO,QAAQ;AAAA,QACtC,SAAS,OAAO,aAAa,SAAS;AAAA,QACtC,gBAAgB,OAAO,aAAa,gBAAgB;AAAA,MACxD,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,OAAK,iBAAiB,SAAS,OAAO;AAEtC,SAAO;AAAA,IACH,UAAU;AACN,WAAK,oBAAoB,SAAS,OAAO;AAAA,IAC7C;AAAA,EACJ;AACJ;AAkBA,IAAM,QAAQ,CAAC,SAAS;AACpB,QAAM,cAAc,CAAC,SAAS,OAAO;AACjC,WAAO,MAAM,GAAG,YAAY,QAAS,MAAK,GAAG;AAC7C,WAAO;AAAA,EACX;AAEA,QAAM,UAAU,CAAC,UAAU;AACvB,UAAM,SAAS,YAAY,KAAK,MAAM,MAAM;AAC5C,QACI,WACC,OAAO,WAAW,MAAM,OAAO,WAAW,YAC3C,YAAY,MAAM,KAClB,eAAe,KAAK,KACpB,CAAC,OAAO,aAAa,SAAS,GAChC;AACE,YAAM,eAAe;AACrB,eAAS,OAAO,WAAW,OAAO,QAAQ;AAAA,QACtC,SAAS,OAAO,aAAa,SAAS;AAAA,QACtC,gBAAgB,OAAO,aAAa,gBAAgB;AAAA,MACxD,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,OAAK,iBAAiB,SAAS,OAAO;AAEtC,SAAO;AAAA,IACH,UAAU;AACN,WAAK,oBAAoB,SAAS,OAAO;AAAA,IAC7C;AAAA,EACJ;AACJ;", "names": ["score", "ctx", "location", "navigate", "ctx", "_a", "path", "location", "ctx", "location", "base", "activeRoute", "basepath"]}